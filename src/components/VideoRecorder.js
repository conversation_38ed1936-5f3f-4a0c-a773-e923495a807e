import React, { useState, useEffect, useLayoutEffect, useRef, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  <PERSON><PERSON><PERSON>bar,
  <PERSON>lider,
  LinearProgress
} from '@mui/material';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import CircleIcon from '@mui/icons-material/Circle';

import videoStore from '../services/videoStorage';
import { testAWSConnection } from '../services/awsStorage';

import Webcam from 'react-webcam';
import WebcamPermissionHandler from './WebcamPermissionHandler';
import * as faceLandmarksDetection from '@tensorflow-models/face-landmarks-detection';
import * as tf from '@tensorflow/tfjs';
import { createMetadata, updateMetadataManifest, getDeviceInfo, updateCameraResolution } from '../services/metadataService';
import { createLipNetVideo, extractVideoThumbnail } from '../services/videoProcessor';
import { handleError, ERROR_SEVERITY } from '../utils/errorHandler';

// Constants
const VIDEO_CONSTRAINTS = {
  width: 640,
  height: 480,
  frameRate: 30,
  facingMode: 'user'
};

// Region of interest for mouth detection (in pixels)
const MOUTH_ROI = {
  width: 100,
  height: 50
};

// LipNet video processing options
const LIPNET_OPTIONS = {
  targetWidth: 100,
  targetHeight: 50,
  frameRate: 25,
  greyscale: true
};

// Real-time LipNet preprocessing options
const REALTIME_LIPNET_OPTIONS = {
  targetWidth: 150,
  targetHeight: 75,
  frameRate: 25,
  greyscale: false // Temporarily disabled to focus on mouth cropping
};

const VideoRecorder = ({
  onRecordingComplete,
  disabled = false,
  category,
  phrase,
  recordingNumber,
  demographics
}) => {
  // Safeguard: Only allow recording if phrase is explicitly provided
  if (!phrase || phrase.trim() === '') {
    return (
      <Box sx={{ textAlign: 'center', p: 4 }}>
        <Typography variant="h6" color="error">
          No phrase selected for recording
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Please select phrases from the phrase selection page to begin recording.
        </Typography>
      </Box>
    );
  }

  // Refs
  const webcamRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionRef = useRef(null);
  const deviceInfoRef = useRef(getDeviceInfo());

  // Background processing refs for dual video output
  const lipnetCanvasRef = useRef(null);
  const lipnetMediaRecorderRef = useRef(null);
  const frameProcessingRef = useRef(null);

  // Mouth region recording refs for direct capture optimization
  const mouthCanvasRef = useRef(null);
  const mouthRecordingRef = useRef(null);
  
  // State
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [cameraError, setCameraError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordedChunks, setRecordedChunks] = useState([]);
  const [lipnetRecordedChunks, setLipnetRecordedChunks] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [processingPhase, setProcessingPhase] = useState(''); // Track specific processing phase
  const [uploadProgress, setUploadProgress] = useState(0); // Track upload progress if available
  const [selectedCamera, setSelectedCamera] = useState('');
  const [availableCameras, setAvailableCameras] = useState([]);
  const [zoomLevel, setZoomLevel] = useState(1); // Default zoom at 1.0x
  const [canReRecord, setCanReRecord] = useState(false);
  // Removed recordingCount state - using parent data directly
  const [showSavedNotification, setShowSavedNotification] = useState(false);

  // Removed early exit functionality
  const [faceDetected, setFaceDetected] = useState(false);
  const [recordingTimer, setRecordingTimer] = useState(0);
  const [mouthPosition, setMouthPosition] = useState(null);
  const [mouthTrackingQuality, setMouthTrackingQuality] = useState(0);
  const [previousMouthPosition, setPreviousMouthPosition] = useState(null);
  const [landmarkConfidence, setLandmarkConfidence] = useState(0);

  // Progress tracking state
  const [progressData, setProgressData] = useState({
    completedPhrases: 0,
    totalPhrases: 0
  });

  // Current phrase recording count state
  const [currentPhraseRecordingCount, setCurrentPhraseRecordingCount] = useState(0);

  const [lipnetQualityMetrics, setLipnetQualityMetrics] = useState({
    frameCount: 0,
    avgConfidence: 0,
    codecUsed: '',
    processingErrors: 0
  });
  const [lipnetProcessingActive, setLipnetProcessingActive] = useState(false);
  const [model, setModel] = useState(null);


  // Removed complex synchronization logic - using simple parent data flow

  // Device detection
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isLaptop = window.innerWidth > 768;

  // Helper function to calculate progress data
  const calculateProgressData = useCallback(() => {
    try {
      const recordingsCount = JSON.parse(localStorage.getItem('icuAppRecordingsCount') || '{}');
      // Use the correct localStorage key for selected phrases
      const selectedPhrases = JSON.parse(localStorage.getItem('icu_selected_phrases') || '[]');

      const completedCount = Object.values(recordingsCount).filter(count => count >= 3).length;
      const totalCount = selectedPhrases.length;

      return {
        completedPhrases: completedCount,
        totalPhrases: totalCount
      };
    } catch (error) {
      console.warn('Error calculating progress data:', error);
      return {
        completedPhrases: 0,
        totalPhrases: 0
      };
    }
  }, []);

  // Helper function to get current phrase recording count
  const getCurrentPhraseRecordingCount = useCallback(() => {
    try {
      const recordingsCount = JSON.parse(localStorage.getItem('icuAppRecordingsCount') || '{}');
      const phraseKey = `${category}:${phrase}`;
      return recordingsCount[phraseKey] || 0;
    } catch (error) {
      console.warn('Error getting current phrase recording count:', error);
      return 0;
    }
  }, [category, phrase]);

  // Update progress data and current phrase recording count when component mounts and when recordings change
  useEffect(() => {
    const updateProgress = () => {
      const newProgressData = calculateProgressData();
      setProgressData(newProgressData);

      const currentCount = getCurrentPhraseRecordingCount();
      setCurrentPhraseRecordingCount(currentCount);
    };

    updateProgress();

    // Listen for localStorage changes to update progress in real-time
    const handleStorageChange = (e) => {
      if (e.key === 'icuAppRecordingsCount' || e.key === 'icu_selected_phrases') {
        updateProgress();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [calculateProgressData, getCurrentPhraseRecordingCount]);

  // Initialize background canvas for LipNet preprocessing
  const initializeLipNetCanvas = useCallback(() => {
    if (!lipnetCanvasRef.current) {
      const canvas = document.createElement('canvas');
      canvas.width = REALTIME_LIPNET_OPTIONS.targetWidth;
      canvas.height = REALTIME_LIPNET_OPTIONS.targetHeight;
      canvas.style.display = 'none'; // Hidden from UI
      lipnetCanvasRef.current = canvas;

      // Append to document body but keep hidden
      document.body.appendChild(canvas);

      console.log('🎨 Background LipNet canvas initialized:', {
        width: canvas.width,
        height: canvas.height,
        hidden: true
      });
    }
    return lipnetCanvasRef.current;
  }, []);

  // Clean up background canvas
  const cleanupLipNetCanvas = useCallback(() => {
    if (lipnetCanvasRef.current && lipnetCanvasRef.current.parentNode) {
      lipnetCanvasRef.current.parentNode.removeChild(lipnetCanvasRef.current);
      lipnetCanvasRef.current = null;
      console.log('🧹 Background LipNet canvas cleaned up');
    }
  }, []);

  // Initialize mouth region canvas for direct recording optimization
  const initializeMouthCanvas = useCallback(() => {
    if (!mouthCanvasRef.current) {
      const canvas = document.createElement('canvas');
      // ENHANCED CANVAS SIZE: Larger dimensions for better video quality while maintaining mouth-only cropping
      // User will handle post-processing to LipNet 150×75 requirements after recording
      canvas.width = 400;   // Larger width for better quality (2:1 aspect ratio maintained)
      canvas.height = 200;  // Larger height for better quality (2:1 aspect ratio maintained)
      canvas.style.display = 'none'; // Hidden from UI
      mouthCanvasRef.current = canvas;

      // Append to document body but keep hidden
      document.body.appendChild(canvas);

      console.log('👄 Enhanced mouth canvas initialized:', {
        width: canvas.width,
        height: canvas.height,
        aspectRatio: (canvas.width / canvas.height).toFixed(2),
        purpose: 'High-quality mouth-only recording with privacy compliance',
        privacyCompliant: true,
        excludesEyes: true,
        postProcessingNote: 'User will resize to LipNet 150×75 after recording'
      });
    }
    return mouthCanvasRef.current;
  }, []);

  // Clean up mouth canvas
  const cleanupMouthCanvas = useCallback(() => {
    if (mouthCanvasRef.current && mouthCanvasRef.current.parentNode) {
      mouthCanvasRef.current.parentNode.removeChild(mouthCanvasRef.current);
      mouthCanvasRef.current = null;
      console.log('🧹 Mouth canvas cleaned up');
    }
  }, []);

  // CRITICAL FIX: Continuous mouth region drawing for MediaRecorder
  // Use a ref to track recording state to avoid React state timing issues
  const isRecordingRef = useRef(false);

  const drawMouthRegion = useCallback(() => {
    // Use ref instead of state to avoid timing issues
    if (!isRecordingRef.current) {
      console.log('👄 Mouth region drawing stopped - recording not active');
      return;
    }

    if (!mouthCanvasRef.current || !webcamRef.current?.video) {
      console.warn('👄 Missing canvas or video reference, retrying...');
      // Retry immediately if recording is still active
      if (isRecording) {
        mouthRecordingRef.current = requestAnimationFrame(drawMouthRegion);
      }
      return;
    }

    const canvas = mouthCanvasRef.current;
    const ctx = canvas.getContext('2d');
    const video = webcamRef.current.video;

    // Ensure video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      console.warn('👄 Video not ready for mouth region drawing, retrying...');
      // CRITICAL: Always retry if recording is active
      if (isRecording) {
        mouthRecordingRef.current = requestAnimationFrame(drawMouthRegion);
      }
      return;
    }

    try {
      // COMPREHENSIVE FRAME RATE MONITORING: Track drawing frequency and timing
      const frameCount = (window.mouthFrameCount || 0) + 1;
      const currentTime = Date.now();

      // Initialize timing tracking
      if (!window.mouthDrawingStartTime) {
        window.mouthDrawingStartTime = currentTime;
        window.lastFrameTime = currentTime;
        window.frameTimings = [];
      }

      // Calculate frame rate metrics
      const timeSinceLastFrame = currentTime - (window.lastFrameTime || currentTime);
      const totalElapsed = currentTime - window.mouthDrawingStartTime;
      const currentFPS = frameCount > 1 ? (frameCount / (totalElapsed / 1000)) : 0;

      window.mouthFrameCount = frameCount;
      window.lastFrameTime = currentTime;
      window.frameTimings.push(timeSinceLastFrame);

      // Log detailed frame rate analysis every 25 frames (once per second at 25fps)
      if (frameCount % 25 === 0) {
        const avgFrameTime = window.frameTimings.slice(-25).reduce((a, b) => a + b, 0) / 25;
        const avgFPS = 1000 / avgFrameTime;

        console.log('👄 Frame rate analysis:', {
          frameNumber: frameCount,
          currentFPS: currentFPS.toFixed(1),
          avgFPS: avgFPS.toFixed(1),
          avgFrameTime: avgFrameTime.toFixed(1) + 'ms',
          canvasSize: `${canvas.width}x${canvas.height}`,
          videoSize: `${video.videoWidth}x${video.videoHeight}`,
          elapsedTime: (totalElapsed / 1000).toFixed(1) + 's',
          targetFPS: 25
        });

        // Warning if frame rate is too low
        if (avgFPS < 20) {
          console.warn('⚠️ Frame rate below target:', avgFPS.toFixed(1), 'fps (target: 25fps)');
        }
      }

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // CRITICAL FIX: Precise lip-centered cropping for LipNet compatibility
    // Position crop to capture lips clearly while excluding eyes
    const centerX = video.videoWidth / 2;

    // IMPROVED LIP POSITIONING: Focus on the mouth/lip area specifically
    // Position at 65% down from top - this typically captures lips without eyes
    const lipCenterY = video.videoHeight * 0.65;

    // EXACT LIPNET DIMENSIONS: Calculate crop size to fill 150x75 canvas perfectly
    // Use larger source area to ensure good quality when scaled down
    const sourceWidth = Math.min(video.videoWidth * 0.5, 300); // Wider area for better lip capture
    const sourceHeight = sourceWidth / 2.0; // Maintain exact 2:1 aspect ratio

    // Center the crop area on the lip region
    const sourceX = Math.max(0, Math.min(centerX - sourceWidth / 2, video.videoWidth - sourceWidth));
    const sourceY = Math.max(0, Math.min(lipCenterY - sourceHeight / 2, video.videoHeight - sourceHeight));

    // PRIVACY PROTECTION: Ensure crop doesn't go above nose line (60% from top)
    const privacyMaxY = video.videoHeight * 0.6;
    const privacyAdjustedY = Math.max(sourceY, privacyMaxY);

    // Adjust height if we moved the Y position for privacy
    const finalSourceHeight = Math.min(sourceHeight, video.videoHeight - privacyAdjustedY);
    const finalSourceWidth = finalSourceHeight * 2.0; // Maintain 2:1 ratio
    const finalSourceX = Math.max(0, Math.min(centerX - finalSourceWidth / 2, video.videoWidth - finalSourceWidth));

    // Draw the precisely positioned lip region to fill the 150x75 canvas
    ctx.drawImage(
      video,
      finalSourceX, privacyAdjustedY, finalSourceWidth, finalSourceHeight,
      0, 0, canvas.width, canvas.height
    );

    // Debug logging for lip positioning (occasional)
    if (Math.random() < 0.01) {
      console.log('👄 Lip-centered crop details:', {
        sourceArea: `${finalSourceX.toFixed(1)},${privacyAdjustedY.toFixed(1)} ${finalSourceWidth.toFixed(1)}x${finalSourceHeight.toFixed(1)}`,
        targetCanvas: `${canvas.width}x${canvas.height}`,
        lipCenterY: lipCenterY.toFixed(1),
        privacyCompliant: privacyAdjustedY >= privacyMaxY,
        aspectRatio: (finalSourceWidth / finalSourceHeight).toFixed(2)
      });
    }

    // ENHANCED LIP DETECTION: Use detected mouth position if available and confident
    // Apply the same precise lip positioning as the fallback method
    if (mouthPosition && mouthPosition.confidence > 0.8) {
      // Use detected mouth center but apply our precise lip positioning logic
      const detectedCenterX = mouthPosition.centerX || (mouthPosition.x + mouthPosition.width / 2);
      const detectedCenterY = mouthPosition.centerY || (mouthPosition.y + mouthPosition.height / 2);

      // PRECISE LIP POSITIONING: Use detected center but ensure proper lip focus
      // Calculate optimal crop size for 150x75 output
      const optimalSourceWidth = Math.min(video.videoWidth * 0.5, 300);
      const optimalSourceHeight = optimalSourceWidth / 2.0; // Exact 2:1 ratio

      // Center on detected mouth but ensure lip visibility
      let enhancedX = Math.max(0, Math.min(detectedCenterX - optimalSourceWidth / 2, video.videoWidth - optimalSourceWidth));
      let enhancedY = Math.max(0, Math.min(detectedCenterY - optimalSourceHeight / 2, video.videoHeight - optimalSourceHeight));

      // PRIVACY PROTECTION: Ensure we don't go above nose line
      const privacyMaxY = video.videoHeight * 0.6;
      enhancedY = Math.max(enhancedY, privacyMaxY);

      // Adjust dimensions if privacy constraint affected positioning
      const finalHeight = Math.min(optimalSourceHeight, video.videoHeight - enhancedY);
      const finalWidth = finalHeight * 2.0; // Maintain 2:1 ratio
      enhancedX = Math.max(0, Math.min(detectedCenterX - finalWidth / 2, video.videoWidth - finalWidth));

      // Only use enhanced detection if it provides a reasonable crop area
      if (finalWidth > 100 && finalHeight > 50 && enhancedY >= privacyMaxY) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(
          video,
          enhancedX, enhancedY, finalWidth, finalHeight,
          0, 0, canvas.width, canvas.height
        );

        console.log('👄 Enhanced lip detection used:', {
          detectedCenter: `${detectedCenterX.toFixed(1)},${detectedCenterY.toFixed(1)}`,
          finalCrop: `${enhancedX.toFixed(1)},${enhancedY.toFixed(1)} ${finalWidth.toFixed(1)}x${finalHeight.toFixed(1)}`,
          confidence: mouthPosition.confidence.toFixed(2),
          aspectRatio: (finalWidth / finalHeight).toFixed(2),
          privacyCompliant: enhancedY >= privacyMaxY
        });
      }
    }

      // CRITICAL VALIDATION: Verify canvas content is actually changing
      if (frameCount % 30 === 0) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;
        let hasContent = false;
        let pixelSum = 0;

        // Check for non-black pixels and calculate checksum
        for (let i = 0; i < pixels.length; i += 4) {
          const r = pixels[i], g = pixels[i + 1], b = pixels[i + 2];
          pixelSum += r + g + b;
          if (r > 10 || g > 10 || b > 10) {
            hasContent = true;
          }
        }

        console.log('👄 Canvas content validation:', {
          hasContent,
          pixelChecksum: pixelSum,
          canvasUpdating: pixelSum !== window.lastPixelSum,
          frameNumber: frameCount
        });

        window.lastPixelSum = pixelSum;

        if (!hasContent) {
          console.warn('⚠️ Canvas appears to have no video content during recording!');
        }
      }

    } catch (error) {
      console.warn('👄 Error drawing mouth region:', error);
      // Continue with fallback black frame but keep drawing loop active
      try {
        ctx.fillStyle = 'black';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      } catch (fallbackError) {
        console.error('👄 Critical error in mouth region drawing:', fallbackError);
      }
    }

    // CRITICAL: ALWAYS continue drawing if recording is active (using ref for immediate state)
    // This is essential for MediaRecorder to receive video data
    if (isRecordingRef.current) {
      mouthRecordingRef.current = requestAnimationFrame(drawMouthRegion);
    } else {
      console.log('👄 Drawing loop stopped - recording ended');
    }
  }, [mouthPosition]); // Removed isRecording dependency to avoid timing issues

  // Real-time frame processing for LipNet preprocessing with quality control
  const processFrameForLipNet = useCallback(() => {
    if (!webcamRef.current || !webcamRef.current.video || !lipnetCanvasRef.current || !lipnetProcessingActive) {
      return;
    }



    const video = webcamRef.current.video;
    const canvas = lipnetCanvasRef.current;
    const ctx = canvas.getContext('2d');

    // Check if video is ready
    if (video.videoWidth === 0 || video.videoHeight === 0) {
      frameProcessingRef.current = requestAnimationFrame(processFrameForLipNet);
      return;
    }

    // Update frame count for quality metrics
    setLipnetQualityMetrics(prev => ({
      ...prev,
      frameCount: prev.frameCount + 1,
      avgConfidence: (prev.avgConfidence * prev.frameCount + landmarkConfidence) / (prev.frameCount + 1)
    }));

    try {
      // Enhanced mouth ROI calculation with auto-centering and fallback
      let cropArea;
      if (mouthPosition && mouthPosition.confidence > 0.7) {
        // Use enhanced mouth position with auto-centering
        cropArea = {
          x: mouthPosition.x,
          y: mouthPosition.y,
          width: mouthPosition.width,
          height: mouthPosition.height
        };
        console.log('🎯 High confidence crop area:', cropArea);
      } else if (mouthPosition && mouthPosition.confidence > 0.3) {
        // Low confidence - use basic mouth position with extra padding
        const padding = 30;
        cropArea = {
          x: Math.max(0, mouthPosition.x - padding),
          y: Math.max(0, mouthPosition.y - padding),
          width: Math.min(video.videoWidth - (mouthPosition.x - padding), mouthPosition.width + 2 * padding),
          height: Math.min(video.videoHeight - (mouthPosition.y - padding), mouthPosition.height + 2 * padding)
        };
        console.log('🎯 Low confidence crop area:', cropArea);
      } else {
        // Fallback to center crop focused on lower face area with 2:1 aspect ratio
        const targetAspectRatio = 150 / 75; // 2:1 for LipNet
        const fallbackWidth = 160;
        const fallbackHeight = fallbackWidth / targetAspectRatio; // 80px
        cropArea = {
          x: (video.videoWidth - fallbackWidth) / 2,
          y: (video.videoHeight - fallbackHeight) / 2 + 40, // Offset down for mouth area
          width: fallbackWidth,
          height: fallbackHeight
        };
        console.log('🎯 Fallback crop area:', cropArea);
      }

      // Validate crop area bounds
      cropArea.x = Math.max(0, Math.min(cropArea.x, video.videoWidth - cropArea.width));
      cropArea.y = Math.max(0, Math.min(cropArea.y, video.videoHeight - cropArea.height));
      cropArea.width = Math.min(cropArea.width, video.videoWidth - cropArea.x);
      cropArea.height = Math.min(cropArea.height, video.videoHeight - cropArea.y);

      console.log('🎯 Final validated crop area:', {
        x: cropArea.x.toFixed(1),
        y: cropArea.y.toFixed(1),
        width: cropArea.width.toFixed(1),
        height: cropArea.height.toFixed(1),
        aspectRatio: (cropArea.width / cropArea.height).toFixed(2)
      });

      // Clear canvas with black background
      ctx.fillStyle = 'black';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw the cropped mouth region to exactly 150x75 pixels
      ctx.drawImage(
        video,
        cropArea.x, cropArea.y, cropArea.width, cropArea.height,
        0, 0, REALTIME_LIPNET_OPTIONS.targetWidth, REALTIME_LIPNET_OPTIONS.targetHeight
      );

      console.log('🎯 Drew mouth ROI to canvas:', {
        sourceArea: `${cropArea.x.toFixed(1)},${cropArea.y.toFixed(1)} ${cropArea.width.toFixed(1)}x${cropArea.height.toFixed(1)}`,
        targetSize: `${REALTIME_LIPNET_OPTIONS.targetWidth}x${REALTIME_LIPNET_OPTIONS.targetHeight}`
      });

      // Apply grayscale conversion if enabled (temporarily disabled for debugging)
      if (REALTIME_LIPNET_OPTIONS.greyscale) {
        // Apply grayscale conversion using pixel manipulation for reliability
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        for (let i = 0; i < data.length; i += 4) {
          // Use standard luminance formula for grayscale conversion
          const gray = Math.round(data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114);
          data[i] = gray;     // Red
          data[i + 1] = gray; // Green
          data[i + 2] = gray; // Blue
          // Alpha channel (data[i + 3]) remains unchanged
        }

        ctx.putImageData(imageData, 0, 0);
        console.log('🎨 Applied grayscale conversion to mouth ROI');
      }

      // Verify final canvas dimensions
      console.log('🎯 Final canvas verification:', {
        canvasSize: `${canvas.width}x${canvas.height}`,
        expectedSize: `${REALTIME_LIPNET_OPTIONS.targetWidth}x${REALTIME_LIPNET_OPTIONS.targetHeight}`,
        matches: canvas.width === REALTIME_LIPNET_OPTIONS.targetWidth && canvas.height === REALTIME_LIPNET_OPTIONS.targetHeight
      });

    } catch (error) {
      console.warn('Frame processing error:', error);
      setLipnetQualityMetrics(prev => ({
        ...prev,
        processingErrors: prev.processingErrors + 1
      }));
    }

    // Frame-by-frame validation logging (every 25 frames to avoid spam)
    if (lipnetQualityMetrics.frameCount % 25 === 0) {
      console.log(`🎯 Frame validation: ${canvas.width}×${canvas.height}, target: ${REALTIME_LIPNET_OPTIONS.targetWidth}×${REALTIME_LIPNET_OPTIONS.targetHeight}`);
      if (landmarkConfidence < 0.5) {
        console.warn(`🎯 Low landmark confidence warning: ${landmarkConfidence.toFixed(2)}`);
      }

      // Debug: Check if canvas has content
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const hasContent = imageData.data.some(pixel => pixel > 0);
      console.log(`🎯 Canvas content check: ${hasContent ? 'HAS CONTENT' : 'EMPTY'}`);

      // Debug: Sample pixel values to verify content
      if (hasContent) {
        const centerPixelIndex = (Math.floor(canvas.height / 2) * canvas.width + Math.floor(canvas.width / 2)) * 4;
        const centerPixel = {
          r: imageData.data[centerPixelIndex],
          g: imageData.data[centerPixelIndex + 1],
          b: imageData.data[centerPixelIndex + 2]
        };
        console.log(`🎯 Center pixel RGB: ${centerPixel.r}, ${centerPixel.g}, ${centerPixel.b}`);
      }
    }

    // Continue processing if LipNet processing is active
    if (lipnetProcessingActive) {
      frameProcessingRef.current = requestAnimationFrame(processFrameForLipNet);
    }
  }, [lipnetProcessingActive, mouthPosition, landmarkConfidence, lipnetQualityMetrics.frameCount]);

  // Backend upload mode detection - check if using backend upload instead of direct S3
  const isBackendUploadMode = !process.env.REACT_APP_AWS_IDENTITY_POOL_ID ||
                              process.env.REACT_APP_AWS_IDENTITY_POOL_ID === 'your-identity-pool-id-here';

  // Only show development mode if backend URL is also not configured
  const isDevelopmentMode = isBackendUploadMode && !process.env.REACT_APP_BACKEND_URL;
  
  // Instructions for optimal lip/mouth positioning
  const lipPositioningInstructions = [
    'Position your face in the centre of the frame',
    'Ensure your mouth is clearly visible with good lighting',
    'Keep your head still while recording',
    'Speak clearly and at a normal pace',
    'Make sure your chin and lips are fully visible'
  ];
  

  


  // Handle recording complete with dual video support
  const handleRecordingComplete = useCallback(async (originalVideoBlob, lipnetVideoBlob = null) => {
    console.log('=== RECORDING COMPLETION PROCESS STARTED ===');
    console.log('🔄 Processing phase: Video processing and upload preparation');

    // Ensure processing state is active (should already be set by handleStopRecording)
    setProcessing(true);
    setProcessingPhase('processing');

    // DISABLED: Testing mode auto-completion to prevent auto-advancement issues
    // Testing mode was causing recordings to auto-complete without user interaction
    const TESTING_MODE = false; // Permanently disabled to prevent auto-advancement bugs
    if (TESTING_MODE) {
      console.log('🧪 TESTING MODE: Disabled to prevent auto-advancement issues');
      // Testing mode functionality removed to prevent unintended auto-completion
      return;
    }

    console.log('🎬 Original video blob details:', {
      size: originalVideoBlob.size,
      type: originalVideoBlob.type,
      phrase,
      category,
      recordingNumber
    });
    if (lipnetVideoBlob) {
      console.log('🎨 LipNet video blob details:', {
        size: lipnetVideoBlob.size,
        type: lipnetVideoBlob.type
      });
    } else {
      console.log('🎨 No LipNet video blob - using original only');
    }
    console.log('🔧 Environment check:', {
      backendUrl: process.env.REACT_APP_BACKEND_URL,
      hasVideoStore: !!videoStore,
      hasDemographics: !!demographics
    });

    // Parent component will handle recording count updates





    setProcessing(true);
    setIsRecording(false);

    try {
      console.log('Step 1: Creating metadata...');
      const metadata = await createMetadata({
        videoBlob: originalVideoBlob,
        phrase,
        category,
        recordingNumber,
        demographics,
        deviceInfo: deviceInfoRef.current,
        qualityMetrics: { brightness: 0, sharpness: 0, overall: 'good' }, // Simplified quality metrics
        mouthPosition,
        mouthTrackingQuality,
        landmarkConfidence, // Enhanced metadata for LipNet
        zoomLevel,
        timestamp: new Date().toISOString()
      });
      console.log('Metadata created successfully:', metadata);

      console.log('Step 3: Saving recording(s) to storage...');
      console.log('🔄 About to call videoStore with:', {
        originalVideoBlobSize: originalVideoBlob.size,
        lipnetVideoBlobSize: lipnetVideoBlob ? lipnetVideoBlob.size : 'N/A',
        metadataKeys: Object.keys(metadata),
        videoStoreExists: !!videoStore
      });

      // Update processing phase to uploading
      console.log('🔄 Processing phase: Starting AWS S3 upload');
      setProcessingPhase('uploading');
      setUploadProgress(10); // Initial upload progress

      // Add debug mode for upload troubleshooting
      const DEBUG_MODE = window.location.search.includes('debug=true');
      if (DEBUG_MODE) {
        console.log('🐛 DEBUG MODE: Enhanced logging enabled');
        console.log('🔍 Upload environment check:');
        console.log('  - Backend URL:', process.env.REACT_APP_BACKEND_URL);
        console.log('  - AWS Identity Pool:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID);
        console.log('  - Backend server test...');

        try {
          const healthCheck = await fetch('http://localhost:5000/health');
          console.log('  - Backend health status:', healthCheck.status);
          const healthData = await healthCheck.json();
          console.log('  - Backend health data:', healthData);
        } catch (healthError) {
          console.error('  - Backend health check failed:', healthError);
        }
      }

      let savedData;
      if (lipnetVideoBlob) {
        // Use dual video upload if we have both videos
        console.log('📤 Uploading dual videos to AWS S3...');
        savedData = await videoStore.saveDualRecording(originalVideoBlob, lipnetVideoBlob, metadata);
        console.log('✅ Dual recordings saved successfully:', savedData);
      } else {
        // Fallback to single video upload
        console.log('📤 Uploading single video to AWS S3...');
        savedData = await videoStore.saveRecording(originalVideoBlob, metadata);
        console.log('✅ Single recording saved successfully:', savedData);
      }

      // Update upload progress
      setUploadProgress(80);

      console.log('Step 4: Updating metadata manifest...');
      await updateMetadataManifest(metadata);
      console.log('Metadata manifest updated successfully');

      // Complete upload progress
      setUploadProgress(100);

      console.log('Step 5: Updating UI state...');
      setCanReRecord(true);
      setShowSavedNotification(true);

      // Create simplified quality check object for compatibility
      const simplifiedQualityCheck = {
        isValid: true,
        issues: [],
        metrics: {
          brightness: 0,
          sharpness: 0,
          overall: 'good'
        }
      };

      console.log('Step 6: Calling parent callback...');
      console.log('  onRecordingComplete function exists:', !!onRecordingComplete);
      console.log('  savedData:', savedData);
      console.log('  metadata:', metadata);
      console.log('  simplifiedQualityCheck:', simplifiedQualityCheck);

      if (onRecordingComplete) {
        console.log('  🚀 Calling onRecordingComplete...');
        console.log('  📊 VideoRecorder calling parent with data:');
        console.log('    savedData:', savedData);
        console.log('    metadata:', metadata);
        console.log('    simplifiedQualityCheck:', simplifiedQualityCheck);
        console.log('    phrase:', phrase);
        console.log('    category:', category);
        console.log('    recordingNumber:', recordingNumber);
        onRecordingComplete(savedData, metadata, simplifiedQualityCheck);
        console.log('  ✅ onRecordingComplete called successfully');

        // ENHANCED PROGRESS TRACKING: Update progress data and recording counts with debugging
        setTimeout(() => {
          console.log('📊 Updating progress data after recording completion...');

          // Check localStorage before update
          const beforeUpdate = localStorage.getItem('icuAppRecordingsCount');
          console.log('  localStorage before update:', beforeUpdate);

          const newProgressData = calculateProgressData();
          setProgressData(newProgressData);
          console.log('  New progress data:', newProgressData);

          const currentCount = getCurrentPhraseRecordingCount();
          setCurrentPhraseRecordingCount(currentCount);
          console.log('  Current phrase recording count:', currentCount);

          // Check localStorage after update
          const afterUpdate = localStorage.getItem('icuAppRecordingsCount');
          console.log('  localStorage after update:', afterUpdate);

          // Verify the count increased
          if (beforeUpdate && afterUpdate) {
            const beforeCount = JSON.parse(beforeUpdate);
            const afterCount = JSON.parse(afterUpdate);
            const phraseKey = `${phrase}_${category}`;
            const beforePhraseCount = beforeCount[phraseKey] || 0;
            const afterPhraseCount = afterCount[phraseKey] || 0;

            console.log('  Recording count change for', phraseKey + ':', beforePhraseCount, '→', afterPhraseCount);

            if (afterPhraseCount <= beforePhraseCount) {
              console.warn('⚠️ Recording count did not increase properly');
            } else {
              console.log('✅ Recording count increased successfully');
            }
          }
        }, 200); // Increased delay to ensure all updates complete
      } else {
        console.error('  ❌ onRecordingComplete function is missing!');
      }

      console.log('Step 7: Recording completion - parent will handle count updates...');
      console.log('  Parent callback will trigger sync via recordingNumber prop');
      // Don't increment local count here - let parent handle it and sync via props

      console.log('✅ Upload completed successfully - resetting processing state');
      // Reset processing state after successful completion
      setProcessing(false);
      setProcessingPhase('');
      setUploadProgress(0);

      // Stop LipNet processing after successful recording
      setLipnetProcessingActive(false);
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
        frameProcessingRef.current = null;
      }

      console.log('✅ RECORDING COMPLETION PROCESS FINISHED SUCCESSFULLY ===');
      console.log('🎉 Recording saved and processed without errors!');
    } catch (error) {
      console.error('=== ERROR DURING RECORDING COMPLETION ===');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);

      // Enhanced error message handling with specific error types
      let errorMessage = 'Recording processing failed';
      let isDevModeError = false;

      if (error.message) {
        // Check for backend upload mode vs actual development mode
        const isBackendUploadMode = error.message.includes('FORCED BACKEND MODE') ||
                                   error.message.includes('backend upload');
        const isActualDevMode = error.message.includes('your-identity-pool-id-here') ||
                               (error.message.includes('AWS credentials not configured') && !isBackendUploadMode);

        if (isActualDevMode) {
          errorMessage = '✅ Recording completed successfully! (Development mode - AWS not configured)';
          isDevModeError = true;
          console.log('💡 AWS not configured - this is expected in development mode');
          console.log('🎉 Recording processed successfully in simulation mode!');

          // In development mode, treat this as success
          setCanReRecord(true);
          setShowSavedNotification(true);

          if (onRecordingComplete) {
            const mockSavedData = {
              id: `dev-recording-${Date.now()}`,
              url: 'https://simulated-upload/dev-recording.mp4',
              key: 'simulated/dev-recording.mp4',
              filename: 'dev-recording.mp4',
              phrase,
              category,
              timestamp: new Date().toISOString(),
              simulated: true
            };
            const mockMetadata = {
              phrase,
              category,
              recordingNumber,
              timestamp: new Date().toISOString(),
              simulated: true
            };
            console.log('  🎭 Development mode: calling onRecordingComplete with mock data');
            onRecordingComplete(mockSavedData, mockMetadata, { metrics: { overall: 'good' } });
          }

          setErrorMessage('');
          return;
        }

        // Backend upload mode should NOT trigger auto-completion
        if (isBackendUploadMode) {
          console.log('🔄 Backend upload mode detected - this is normal operation, not dev mode');
          console.log('❌ Backend upload failed, but this should not auto-complete the recording');
          // Let the error fall through to normal error handling
        }

        // Handle specific AWS error types
        else if (error.message.includes('AWS_CREDENTIALS_ERROR')) {
          errorMessage = '🔑 AWS credentials not configured properly. Please check your environment variables and refresh the page.';
          console.error('💡 SOLUTION: Add AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY to your .env file');
        } else if (error.message.includes('AWS_ACCESS_DENIED')) {
          errorMessage = '🚫 Access denied to S3 bucket. Please check bucket permissions and IAM policies.';
          console.error('💡 SOLUTION: Verify your AWS user has s3:PutObject permissions for the bucket');
        } else if (error.message.includes('AWS_BUCKET_NOT_FOUND')) {
          errorMessage = '📁 S3 bucket does not exist. Please check the bucket name in your configuration.';
          console.error('💡 SOLUTION: Verify the bucket name in AWS_S3_BUCKET environment variable');
        } else if (error.message.includes('AWS_NETWORK_ERROR')) {
          errorMessage = '🌐 Network error during upload. Please check your internet connection and try again.';
          console.error('💡 SOLUTION: Check your internet connection and AWS service status');
        } else if (error.message.includes('AWS_FILE_TOO_LARGE')) {
          errorMessage = '📏 Recording file is too large for upload. Please try recording again.';
          console.error('💡 SOLUTION: Try recording a shorter video or check S3 upload limits');
        } else if (error.message.includes('VALIDATION_ERROR')) {
          errorMessage = '⚠️ Recording validation failed. Please try recording again.';
          console.error('💡 SOLUTION: Ensure all required fields are provided');
        } else if (error.message.includes('BACKEND_CONNECTION_ERROR')) {
          errorMessage = '🔄 Cannot connect to backend server. Please ensure the server is running and try again.';
          console.error('💡 SOLUTION: Start the backend server with: cd server && npm start');
        } else if (error.message.includes('BACKEND_NETWORK_ERROR')) {
          errorMessage = '🌐 Network error connecting to backend. Please check your connection and try again.';
          console.error('💡 SOLUTION: Check your internet connection and backend server status');
        } else if (error.message.includes('BACKEND_SERVER_ERROR')) {
          errorMessage = '🔄 Backend server error. Please check server logs for details.';
          console.error('💡 SOLUTION: Check server console for detailed error information');
        } else if (error.message.includes('BACKEND_UPLOAD_ERROR')) {
          errorMessage = '📤 Backend upload failed. Please try again or check server status.';
          console.error('💡 SOLUTION: Check backend server logs and AWS configuration');
        } else if (error.message.includes('Backend upload failed')) {
          errorMessage = '🔄 Backend upload failed. Please check server logs for details.';
          console.error('💡 SOLUTION: Check server console for detailed error information');
        }

        // Handle general error types
        else if (error.message.includes('Access denied')) {
          errorMessage = '🚫 AWS upload failed: Access denied. Please check S3 bucket permissions.';
        } else if (error.message.includes('NoSuchBucket')) {
          errorMessage = '📁 AWS upload failed: S3 bucket does not exist.';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = '🌐 Network error during upload. Please check your connection and try again.';
        } else if (error.message.includes('413') || error.message.includes('too large')) {
          errorMessage = '📏 Recording file is too large. Please try again.';
        } else if (error.message.includes('metadata')) {
          errorMessage = '📋 Error creating recording metadata. Please try again.';
        } else if (error.message.includes('storage') || error.message.includes('S3')) {
          errorMessage = '💾 Error saving recording to storage. Please try again.';
        } else {
          errorMessage = `❌ Recording processing failed: ${error.message}`;
        }
      }

      // Only set error message if this is not a development mode "error"
      if (!isDevModeError) {
        console.log('❌ Upload failed - attempting local fallback save');

        // Try to save locally as fallback
        try {
          const localSaveData = {
            id: `local-${Date.now()}`,
            phrase,
            category,
            recordingNumber,
            timestamp: new Date().toISOString(),
            size: originalVideoBlob.size,
            type: originalVideoBlob.type,
            saved: 'locally'
          };

          // Store in localStorage for later retry
          const existingLocalSaves = JSON.parse(localStorage.getItem('icuAppLocalSaves') || '[]');
          existingLocalSaves.push(localSaveData);
          localStorage.setItem('icuAppLocalSaves', JSON.stringify(existingLocalSaves));

          console.log('✅ Recording saved locally for later upload retry');
          setErrorMessage(errorMessage + '\n\n📱 Recording saved locally and will be retried later.');
        } catch (localSaveError) {
          console.error('❌ Local save also failed:', localSaveError);
          setErrorMessage(errorMessage);
        }
      }
    } finally {
      // Always reset processing state in finally block to ensure button re-enablement
      console.log('🔄 Resetting processing state (finally block)');
      setProcessing(false);
      setProcessingPhase('');
      setUploadProgress(0);

      // Stop LipNet processing in finally block to ensure cleanup
      setLipnetProcessingActive(false);
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
        frameProcessingRef.current = null;
      }
    }
  }, [phrase, category, recordingNumber, demographics, mouthPosition, mouthTrackingQuality, zoomLevel, onRecordingComplete]);

  // Handle stopping recording
  const handleStopRecording = useCallback(async () => {
    if (mediaRecorderRef.current && (isRecording || isRecordingRef.current)) {
      console.log('🛑 Stopping recording and entering processing state');

      // IMMEDIATE STATE CLEANUP: Stop drawing loop immediately
      isRecordingRef.current = false;
      setIsRecording(false);

      // Stop mouth region drawing
      if (mouthRecordingRef.current) {
        cancelAnimationFrame(mouthRecordingRef.current);
        mouthRecordingRef.current = null;
        console.log('👄 Mouth region drawing stopped');
      }

      mediaRecorderRef.current.stop();

      // Stop LipNet processing to prevent infinite loops
      setLipnetProcessingActive(false);
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
        frameProcessingRef.current = null;
      }

      // Immediately set processing state to prevent concurrent recordings
      setProcessing(true);
      setProcessingPhase('processing');
      setUploadProgress(0);
      console.log('🔄 Processing state activated - buttons disabled');
    }
  }, [isRecording]);

  // Removed early exit handlers

  // Check camera permissions
  const checkCameraPermissions = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      
      setAvailableCameras(videoDevices);
      if (videoDevices.length > 0 && !selectedCamera) {
        setSelectedCamera(videoDevices[0].deviceId);
      }
      
      stream.getTracks().forEach(track => track.stop());
      setPermissionGranted(true);
      setCameraError(false);
      setErrorMessage('');
    } catch (err) {
      const errorInfo = handleError(err, 'Camera');
      setCameraError(true);
      setPermissionGranted(false);
      setErrorMessage(errorInfo.message);
    }
  }, [selectedCamera]);
  
  // Video constraints for the webcam
  const getVideoConstraints = useCallback(() => {
    return {
      ...VIDEO_CONSTRAINTS,
      deviceId: selectedCamera ? { exact: selectedCamera } : undefined,
    };
  }, [selectedCamera]);
  
  // Handle camera change
  const handleCameraChange = useCallback((event) => {
    setSelectedCamera(event.target.value);
  }, []);
  


  // Run face detection and mouth tracking
  const detectFace = useCallback(async () => {
    if (!webcamRef.current || !webcamRef.current.video) {
      detectionRef.current = requestAnimationFrame(detectFace);
      return;
    }

    // If no model is available, skip face detection but continue other quality checks
    if (!model) {
      setFaceDetected(false);
      setMouthPosition(null);
      setMouthTrackingQuality(0);

      detectionRef.current = requestAnimationFrame(detectFace);
      return;
    }

    try {
      const video = webcamRef.current.video;

      // Debug: Log that face detection is running (occasionally to avoid spam)
      if (Math.random() < 0.01) { // Log 1% of the time
        console.log('🎯 Face detection loop running, video size:', video.videoWidth, 'x', video.videoHeight, 'model available:', !!model);
      }

      // Check if video dimensions are valid before face detection
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.warn('Video dimensions not available for face detection');
        setFaceDetected(false);
        setMouthPosition(null);
        setMouthTrackingQuality(0);
        return;
      }

      const faces = await model.estimateFaces(video, false);



      if (faces.length > 0) {
        const face = faces[0];
        setFaceDetected(true);

        // Enhanced mouth landmarks extraction with precise lip coordinates
        const keypoints = face.keypoints;
        if (keypoints && keypoints.length > 0) {
          // MediaPipe FaceMesh lip landmark indices (empirically tested)
          // Based on MediaPipe FaceMesh 468 landmarks documentation
          const outerLipIndices = [
            61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
          ];
          const innerLipIndices = [
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308
          ];
          const mouthCornerIndices = [61, 291]; // Left and right mouth corners

          const allLipIndices = [...new Set([...outerLipIndices, ...innerLipIndices, ...mouthCornerIndices])];
          console.log('🎯 Testing lip landmark indices:', allLipIndices);

          const lipPoints = keypoints.filter((_, index) => allLipIndices.includes(index));



          if (lipPoints.length > 0) {
            // Calculate precise mouth bounding box
            const xs = lipPoints.map(p => p.x);
            const ys = lipPoints.map(p => p.y);

            const minX = Math.min(...xs);
            const maxX = Math.max(...xs);
            const minY = Math.min(...ys);
            const maxY = Math.max(...ys);

            const mouthWidth = maxX - minX;
            const mouthHeight = maxY - minY;
            const mouthCenterX = (minX + maxX) / 2;
            const mouthCenterY = (minY + maxY) / 2;

            console.log('🎯 Mouth detection:', {
              center: { x: mouthCenterX.toFixed(1), y: mouthCenterY.toFixed(1) },
              size: { width: mouthWidth.toFixed(1), height: mouthHeight.toFixed(1) },
              bounds: { minX: minX.toFixed(1), maxX: maxX.toFixed(1), minY: minY.toFixed(1), maxY: maxY.toFixed(1) }
            });

            // Calculate landmark confidence based on detection consistency and face detection quality
            const detectionRatio = lipPoints.length / allLipIndices.length;
            const faceScore = face.faceInViewConfidence || face.score || 0.5; // Use face detection confidence if available
            const confidence = Math.min(1, detectionRatio * faceScore);
            setLandmarkConfidence(confidence);

            console.log('🎯 Landmark confidence calculation:', {
              lipPointsDetected: lipPoints.length,
              expectedPoints: allLipIndices.length,
              detectionRatio: detectionRatio.toFixed(2),
              faceScore: faceScore.toFixed(2),
              finalConfidence: confidence.toFixed(2)
            });



            // Enhanced padding calculation for optimal LipNet crop (150x75 target)
            // Calculate padding to achieve target aspect ratio (2:1 width:height)
            const targetAspectRatio = 150 / 75; // 2:1
            const currentAspectRatio = mouthWidth / mouthHeight;

            let finalCropWidth, finalCropHeight;

            if (currentAspectRatio > targetAspectRatio) {
              // Mouth is too wide, height should determine final size
              finalCropHeight = Math.max(mouthHeight * 2.5, 60); // Minimum 60px height
              finalCropWidth = finalCropHeight * targetAspectRatio;
            } else {
              // Mouth is too tall, width should determine final size
              finalCropWidth = Math.max(mouthWidth * 2.0, 120); // Minimum 120px width
              finalCropHeight = finalCropWidth / targetAspectRatio;
            }

            console.log('🎯 Crop dimensions calculated:', {
              finalWidth: finalCropWidth.toFixed(1),
              finalHeight: finalCropHeight.toFixed(1),
              aspectRatio: (finalCropWidth / finalCropHeight).toFixed(2),
              targetAspectRatio: targetAspectRatio.toFixed(2)
            });

            let newMouthRegion = {
              x: Math.max(0, mouthCenterX - finalCropWidth / 2),
              y: Math.max(0, mouthCenterY - finalCropHeight / 2),
              width: Math.min(video.videoWidth - Math.max(0, mouthCenterX - finalCropWidth / 2), finalCropWidth),
              height: Math.min(video.videoHeight - Math.max(0, mouthCenterY - finalCropHeight / 2), finalCropHeight),
              centerX: mouthCenterX,
              centerY: mouthCenterY,
              confidence: confidence
            };

            // Implement smooth transitions to avoid jittery movement
            if (previousMouthPosition && confidence > 0.7) {
              const smoothingFactor = 0.7; // Higher = smoother but slower response
              newMouthRegion = {
                x: previousMouthPosition.x * smoothingFactor + newMouthRegion.x * (1 - smoothingFactor),
                y: previousMouthPosition.y * smoothingFactor + newMouthRegion.y * (1 - smoothingFactor),
                width: previousMouthPosition.width * smoothingFactor + newMouthRegion.width * (1 - smoothingFactor),
                height: previousMouthPosition.height * smoothingFactor + newMouthRegion.height * (1 - smoothingFactor),
                centerX: newMouthRegion.centerX,
                centerY: newMouthRegion.centerY,
                confidence: confidence
              };
            }

            setPreviousMouthPosition(newMouthRegion);
            setMouthPosition(newMouthRegion);

            // Enhanced quality calculation with confidence weighting
            const sizeQuality = Math.min(1, (mouthWidth * mouthHeight) / (MOUTH_ROI.width * MOUTH_ROI.height));
            const overallQuality = (sizeQuality * 0.7) + (confidence * 0.3);
            setMouthTrackingQuality(overallQuality);

            // Log tracking confidence for debugging
            if (isRecording) {
              console.log(`🎨 Mouth tracking confidence: ${confidence.toFixed(2)}`);
            }
          }
        }
      } else {
        setFaceDetected(false);
        setMouthPosition(null);
        setMouthTrackingQuality(0);
        setLandmarkConfidence(0);
        setPreviousMouthPosition(null);

        // Log occasionally for debugging
        if (Math.random() < 0.01) { // Log 1% of the time
          console.log('🎯 No faces detected in current frame');
        }
      }
    } catch (error) {
      console.warn('Face detection error:', error);
      console.warn('Face detection error details:', {
        modelAvailable: !!model,
        videoReady: video.videoWidth > 0 && video.videoHeight > 0,
        errorMessage: error.message
      });
      setFaceDetected(false);
      setMouthPosition(null);
      setMouthTrackingQuality(0);
      setLandmarkConfidence(0);
      setPreviousMouthPosition(null);
    }



    // Continue detection loop
    detectionRef.current = requestAnimationFrame(detectFace);
  }, [model]);

  // Handle starting recording
  const handleStartRecording = useCallback(async () => {
    try {
      // Prevent concurrent recording attempts
      if (processing || isRecording) {
        console.log('🚫 Recording attempt blocked - already processing or recording');
        console.log('  processing:', processing, 'isRecording:', isRecording);
        return;
      }

      console.log('🎬 Starting new recording session');
      // Clear any previous error messages
      setErrorMessage('');

      // Validate camera availability
      if (!webcamRef.current || !webcamRef.current.stream) {
        setErrorMessage('Camera not available. Please check camera permissions.');
        return;
      }

      // Validate video stream is active
      const stream = webcamRef.current.stream;
      const videoTracks = stream.getVideoTracks();
      if (videoTracks.length === 0 || !videoTracks[0].enabled) {
        setErrorMessage('Video stream not active. Please refresh the page.');
        return;
      }

      // ENHANCED BROWSER COMPATIBILITY CHECK
      if (!window.MediaRecorder) {
        setErrorMessage('Recording not supported by your browser. Please use Chrome, Firefox, or Safari.');
        return;
      }

      // Check canvas.captureStream support
      const testCanvas = document.createElement('canvas');
      if (!testCanvas.captureStream) {
        setErrorMessage('Canvas video capture not supported by your browser. Please use a modern browser.');
        return;
      }

      // Check specific codec support for better error messages
      const codecSupport = {
        vp9: MediaRecorder.isTypeSupported('video/webm;codecs=vp9'),
        vp8: MediaRecorder.isTypeSupported('video/webm;codecs=vp8'),
        webm: MediaRecorder.isTypeSupported('video/webm')
      };

      console.log('🔍 Browser compatibility check:', {
        mediaRecorder: !!window.MediaRecorder,
        captureStream: !!testCanvas.captureStream,
        codecSupport
      });

      if (!codecSupport.vp9 && !codecSupport.vp8 && !codecSupport.webm) {
        setErrorMessage('Video recording codecs not supported by your browser. Please try a different browser.');
        return;
      }

      // PRIVACY COMPLIANCE: Use mouth canvas recording for privacy protection
      console.log('🔒 PRIVACY MODE: Using mouth-region-only recording for compliance');
      console.log('👄 Recording only mouth movements - no eyes or upper face data');

      // Initialize mouth canvas for privacy-compliant recording
      const mouthCanvas = initializeMouthCanvas();
      if (!mouthCanvas) {
        throw new Error('Failed to initialize mouth canvas for privacy recording');
      }

      // CRITICAL FIX: Ensure canvas has initial content before creating stream
      console.log('👄 Pre-populating mouth canvas with initial frame...');
      const ctx = mouthCanvas.getContext('2d');
      const video = webcamRef.current.video;

      // Draw initial lip-centered frame to canvas to ensure it has content
      if (video.videoWidth > 0 && video.videoHeight > 0) {
        // PRECISE LIP POSITIONING: Use same logic as main drawing function
        const centerX = video.videoWidth / 2;
        const lipCenterY = video.videoHeight * 0.65; // Focus on lip area

        // Calculate optimal crop size for 150x75 output
        const sourceWidth = Math.min(video.videoWidth * 0.5, 300);
        const sourceHeight = sourceWidth / 2.0; // Exact 2:1 ratio

        // Center on lip region
        const sourceX = Math.max(0, Math.min(centerX - sourceWidth / 2, video.videoWidth - sourceWidth));
        const sourceY = Math.max(0, Math.min(lipCenterY - sourceHeight / 2, video.videoHeight - sourceHeight));

        // Privacy protection: don't go above nose line
        const privacyMaxY = video.videoHeight * 0.6;
        const adjustedY = Math.max(sourceY, privacyMaxY);
        const finalHeight = Math.min(sourceHeight, video.videoHeight - adjustedY);
        const finalWidth = finalHeight * 2.0;
        const finalX = Math.max(0, Math.min(centerX - finalWidth / 2, video.videoWidth - finalWidth));

        ctx.drawImage(
          video,
          finalX, adjustedY, finalWidth, finalHeight,
          0, 0, mouthCanvas.width, mouthCanvas.height
        );
        console.log('👄 Initial lip-centered frame drawn to mouth canvas');
        console.log('🔒 Frame positioned for optimal lip visibility while excluding eyes');
      } else {
        // Fallback: draw a black frame to ensure canvas has content
        ctx.fillStyle = 'black';
        ctx.fillRect(0, 0, mouthCanvas.width, mouthCanvas.height);
        console.log('👄 Fallback black frame drawn to mouth canvas');
      }

      // CRITICAL FIX: Ensure canvas has content BEFORE creating stream
      // Wait for canvas to have actual video content to prevent empty streams
      let streamCreationAttempts = 0;
      let mouthStream = null;

      while (!mouthStream && streamCreationAttempts < 5) {
        streamCreationAttempts++;
        console.log(`👄 Stream creation attempt ${streamCreationAttempts}/5`);

        // Force a frame draw to ensure canvas has content
        if (video.videoWidth > 0 && video.videoHeight > 0) {
          const centerX = video.videoWidth / 2;
          const lipCenterY = video.videoHeight * 0.65;
          const sourceWidth = Math.min(video.videoWidth * 0.5, 300);
          const sourceHeight = sourceWidth / 2.0;
          const sourceX = Math.max(0, Math.min(centerX - sourceWidth / 2, video.videoWidth - sourceWidth));
          const sourceY = Math.max(0, Math.min(lipCenterY - sourceHeight / 2, video.videoHeight - sourceHeight));
          const privacyMaxY = video.videoHeight * 0.6;
          const adjustedY = Math.max(sourceY, privacyMaxY);

          ctx.drawImage(
            video,
            sourceX, adjustedY, sourceWidth, sourceHeight,
            0, 0, mouthCanvas.width, mouthCanvas.height
          );
        }

        // Wait a moment for canvas to update
        await new Promise(resolve => setTimeout(resolve, 200));

        // CRITICAL: Create stream with optimal frame rate for high-quality capture
        try {
          // ENHANCED FRAME RATE: Use 30fps for better frame capture, then optimize MediaRecorder
          mouthStream = mouthCanvas.captureStream(30); // Increased from 25fps to 30fps

          if (mouthStream && mouthStream.getVideoTracks().length > 0) {
            // COMPREHENSIVE VALIDATION: Check stream configuration and capabilities
            const videoTrack = mouthStream.getVideoTracks()[0];
            const trackSettings = videoTrack.getSettings();
            const trackCapabilities = videoTrack.getCapabilities ? videoTrack.getCapabilities() : {};

            console.log('✅ Enhanced mouth canvas stream created:', {
              streamId: mouthStream.id,
              videoTracks: mouthStream.getVideoTracks().length,
              trackSettings: trackSettings,
              frameRate: trackSettings.frameRate || 30,
              width: trackSettings.width || 400,
              height: trackSettings.height || 200,
              capabilities: trackCapabilities,
              canvasSize: `${mouthCanvas.width}x${mouthCanvas.height}`
            });

            // Verify track is not muted or ended
            if (videoTrack.muted) {
              console.warn('⚠️ Video track is muted');
            }
            if (videoTrack.readyState === 'ended') {
              console.warn('⚠️ Video track is ended');
              mouthStream = null;
              continue;
            }

            // Additional validation for frame rate
            const actualFrameRate = trackSettings.frameRate;
            if (actualFrameRate && actualFrameRate < 25) {
              console.warn('⚠️ Stream frame rate lower than expected:', actualFrameRate);
            }

            break;
          } else {
            console.warn(`⚠️ Stream creation attempt ${streamCreationAttempts} failed - no video tracks`);
            mouthStream = null;
          }
        } catch (streamError) {
          console.warn(`⚠️ Stream creation attempt ${streamCreationAttempts} failed:`, streamError);
          mouthStream = null;
        }
      }

      if (!mouthStream) {
        throw new Error('Failed to create mouth canvas stream after multiple attempts');
      }

      // Verify stream has video tracks
      const mouthVideoTracks = mouthStream.getVideoTracks();
      if (mouthVideoTracks.length === 0) {
        throw new Error('Mouth canvas stream has no video tracks');
      }

      console.log('👄 Stream validation successful:', {
        streamActive: mouthStream.active,
        videoTracks: mouthVideoTracks.length,
        trackSettings: mouthVideoTracks[0].getSettings(),
        canvasSize: `${mouthCanvas.width}x${mouthCanvas.height}`
      });

      // CRITICAL FIX: Start mouth region drawing with immediate state synchronization
      console.log('👄 Starting continuous mouth region capture...');

      // IMMEDIATE STATE SETTING: Use ref for instant activation (no React state delay)
      isRecordingRef.current = true;
      setIsRecording(true); // Also set state for UI consistency

      // Initialize frame counter for debugging
      window.mouthFrameCount = 0;
      window.lastPixelSum = 0;

      console.log('👄 Recording ref set to:', isRecordingRef.current);
      console.log('👄 Starting drawing loop immediately...');

      // Start the drawing loop immediately with ref-based state
      mouthRecordingRef.current = requestAnimationFrame(drawMouthRegion);

      // IMMEDIATE VALIDATION: Check that drawing loop will not exit early
      if (!isRecordingRef.current) {
        throw new Error('CRITICAL: Recording ref not set properly - drawing loop will fail');
      }

      // FALLBACK MECHANISM: Start additional drawing loops to ensure activation
      setTimeout(() => {
        if (isRecordingRef.current && window.mouthFrameCount === 0) {
          console.warn('⚠️ Primary drawing loop may have failed, starting fallback...');
          mouthRecordingRef.current = requestAnimationFrame(drawMouthRegion);
        }
      }, 100);

      setTimeout(() => {
        if (isRecordingRef.current && window.mouthFrameCount < 3) {
          console.warn('⚠️ Drawing loop slow, starting additional fallback...');
          mouthRecordingRef.current = requestAnimationFrame(drawMouthRegion);
        }
      }, 300);

      // Wait for canvas to have stable content and drawing loop to be active
      console.log('👄 Waiting for canvas stabilization and continuous drawing...');
      await new Promise(resolve => setTimeout(resolve, 1000)); // Reduced wait time since ref is immediate

      // Verify drawing loop is active
      const frameCountAfterWait = window.mouthFrameCount || 0;
      console.log('👄 Frame count after wait:', frameCountAfterWait);

      if (frameCountAfterWait < 5) {
        console.error('❌ CRITICAL: Drawing loop is not active - frame count:', frameCountAfterWait);
        console.error('  Recording ref state:', isRecordingRef.current);
        console.error('  Recording state:', isRecording);
        throw new Error(`Canvas drawing loop is not updating - frame count: ${frameCountAfterWait}`);
      } else {
        console.log('✅ Drawing loop confirmed active - frame count:', frameCountAfterWait);
        console.log('  Drawing rate:', (frameCountAfterWait / 1.0).toFixed(1), 'fps');
      }

      // CRITICAL FIX: Validate that canvas is actually receiving content
      const validationCtx = mouthCanvas.getContext('2d');
      const imageData = validationCtx.getImageData(0, 0, mouthCanvas.width, mouthCanvas.height);
      const pixels = imageData.data;
      let hasContent = false;

      // Check if canvas has non-black pixels (indicating actual video content)
      for (let i = 0; i < pixels.length; i += 4) {
        if (pixels[i] > 10 || pixels[i + 1] > 10 || pixels[i + 2] > 10) { // RGB values > 10
          hasContent = true;
          break;
        }
      }

      if (!hasContent) {
        console.warn('⚠️ Mouth canvas appears to have no video content, forcing a redraw...');
        // Force one more lip-centered draw cycle
        if (video.videoWidth > 0 && video.videoHeight > 0) {
          // PRECISE LIP POSITIONING: Use same logic as main drawing function
          const centerX = video.videoWidth / 2;
          const lipCenterY = video.videoHeight * 0.65; // Focus on lip area

          // Calculate optimal crop size for 150x75 output
          const sourceWidth = Math.min(video.videoWidth * 0.5, 300);
          const sourceHeight = sourceWidth / 2.0; // Exact 2:1 ratio

          // Center on lip region
          const sourceX = Math.max(0, Math.min(centerX - sourceWidth / 2, video.videoWidth - sourceWidth));
          const sourceY = Math.max(0, Math.min(lipCenterY - sourceHeight / 2, video.videoHeight - sourceHeight));

          // Privacy protection: don't go above nose line
          const privacyMaxY = video.videoHeight * 0.6;
          const adjustedY = Math.max(sourceY, privacyMaxY);
          const finalHeight = Math.min(sourceHeight, video.videoHeight - adjustedY);
          const finalWidth = finalHeight * 2.0;
          const finalX = Math.max(0, Math.min(centerX - finalWidth / 2, video.videoWidth - finalWidth));

          validationCtx.drawImage(
            video,
            finalX, adjustedY, finalWidth, finalHeight,
            0, 0, mouthCanvas.width, mouthCanvas.height
          );
          console.log('👄 Forced lip-centered redraw completed');
          console.log('🔒 Redraw positioned for optimal lip visibility while excluding eyes');
        }
      } else {
        console.log('✅ Mouth canvas has video content, proceeding with recording');
      }

      // Prepare recording chunks
      const recordedChunks = [];
      setRecordedChunks(recordedChunks);

      console.log('👄 Mouth canvas stream details:', {
        streamActive: mouthStream.active,
        videoTracks: mouthStream.getVideoTracks().length,
        audioTracks: mouthStream.getAudioTracks().length,
        canvasSize: `${mouthCanvas.width}x${mouthCanvas.height}`,
        privacyCompliant: true
      });

      // CRITICAL FIX: Create MediaRecorder with robust codec detection and validation
      let mediaRecorder;
      let selectedCodec = 'unknown';

      // Test codec support before creating MediaRecorder
      const codecTests = [
        { mimeType: 'video/webm;codecs=vp9', name: 'VP9', bitrate: 2000000 },
        { mimeType: 'video/webm;codecs=vp8', name: 'VP8', bitrate: 2000000 },
        { mimeType: 'video/webm', name: 'WebM Default', bitrate: 1500000 }
      ];

      for (const codec of codecTests) {
        if (MediaRecorder.isTypeSupported(codec.mimeType)) {
          try {
            console.log(`👄 Testing ${codec.name} codec support...`);
            mediaRecorder = new MediaRecorder(mouthStream, {
              mimeType: codec.mimeType,
              videoBitsPerSecond: codec.bitrate
            });
            selectedCodec = codec.name;
            console.log(`✅ ${codec.name} codec selected for privacy-compliant recording`);
            break;
          } catch (codecError) {
            console.warn(`❌ ${codec.name} codec failed:`, codecError);
            mediaRecorder = null;
          }
        } else {
          console.warn(`❌ ${codec.name} codec not supported by browser`);
        }
      }

      // Final fallback if no specific codec worked
      if (!mediaRecorder) {
        try {
          console.log('👄 Using browser default MediaRecorder configuration...');
          mediaRecorder = new MediaRecorder(mouthStream);
          selectedCodec = 'Browser Default';
          console.log('✅ Default MediaRecorder created successfully');
        } catch (defaultError) {
          throw new Error(`Failed to create MediaRecorder with any codec: ${defaultError.message}`);
        }
      }

      // Validate MediaRecorder state
      if (mediaRecorder.state !== 'inactive') {
        throw new Error(`MediaRecorder in unexpected state: ${mediaRecorder.state}`);
      }

      console.log('👄 MediaRecorder validation successful:', {
        codec: selectedCodec,
        state: mediaRecorder.state,
        streamActive: mouthStream.active,
        mimeType: mediaRecorder.mimeType || 'default'
      });

      mediaRecorderRef.current = mediaRecorder;

      // Note: LipNet processing disabled to prevent infinite loops
      // Using mouth canvas recording for privacy compliance
      console.log('� PRIVACY COMPLIANCE: Using mouth-region-only recording');
      console.log('👄 Recording mouth movements only - no eyes, no audio');
      console.log('📊 LipNet processing disabled to prevent infinite loops');

      // ENHANCED DATA COLLECTION: Comprehensive monitoring and frame rate analysis
      let totalChunksReceived = 0;
      let totalDataSize = 0;
      let firstChunkReceived = false;
      let lastChunkTime = Date.now();
      let recordingStartTime = Date.now();
      let chunkSizes = [];
      let chunkTimings = [];

      // Set up comprehensive monitoring to detect frame capture issues
      const chunkMonitor = setInterval(() => {
        const timeSinceLastChunk = Date.now() - lastChunkTime;
        const totalRecordingTime = Date.now() - recordingStartTime;
        const expectedChunks = Math.floor(totalRecordingTime / 100); // 100ms timeslice
        const chunkRate = totalChunksReceived / (totalRecordingTime / 1000);
        const drawingFrames = window.mouthFrameCount || 0;
        const drawingRate = drawingFrames / (totalRecordingTime / 1000);

        console.log('📊 Recording progress analysis:', {
          recordingTime: (totalRecordingTime / 1000).toFixed(1) + 's',
          chunksReceived: totalChunksReceived,
          expectedChunks: expectedChunks,
          chunkRate: chunkRate.toFixed(1) + '/s',
          drawingFrames: drawingFrames,
          drawingRate: drawingRate.toFixed(1) + 'fps',
          totalDataSize: (totalDataSize / 1024).toFixed(1) + 'KB',
          avgChunkSize: totalChunksReceived > 0 ? (totalDataSize / totalChunksReceived).toFixed(0) + 'B' : '0B'
        });

        if (timeSinceLastChunk > 1000 && mediaRecorder.state === 'recording') {
          console.warn('⚠️ No chunks received for 1+ seconds during recording');
          console.warn('  MediaRecorder state:', mediaRecorder.state);
          console.warn('  Stream active:', mouthStream.active);
          console.warn('  Drawing frames:', drawingFrames);
          console.warn('  Drawing rate:', drawingRate.toFixed(1), 'fps');
          console.warn('  Canvas content changing:', window.lastPixelSum !== window.previousPixelSum);
          window.previousPixelSum = window.lastPixelSum;
        }

        // Warning if chunk rate is too low
        if (totalRecordingTime > 1000 && chunkRate < 5) {
          console.warn('⚠️ Low chunk reception rate:', chunkRate.toFixed(1), '/s (expected: ~10/s)');
        }

        // Warning if drawing rate is too low
        if (totalRecordingTime > 1000 && drawingRate < 20) {
          console.warn('⚠️ Low drawing frame rate:', drawingRate.toFixed(1), 'fps (target: 30fps)');
        }
      }, 1000);

      mediaRecorder.ondataavailable = (event) => {
        lastChunkTime = Date.now();

        if (event.data && event.data.size > 0) {
          totalChunksReceived++;
          totalDataSize += event.data.size;

          // STRICT VALIDATION: Ensure we're getting actual video data
          const isValidVideoChunk = event.data.type && (
            event.data.type.includes('video') ||
            event.data.type.includes('webm') ||
            event.data.type === '' // Some browsers don't set type on chunks
          );

          if (!isValidVideoChunk && event.data.type) {
            console.error('❌ CRITICAL: Received non-video chunk:', event.data.type);
            console.error('  This indicates MediaRecorder is not capturing video properly');
            return; // Don't add non-video chunks
          }

          recordedChunks.push(event.data);

          // Track chunk metrics for analysis
          chunkSizes.push(event.data.size);
          chunkTimings.push(Date.now() - recordingStartTime);

          if (!firstChunkReceived) {
            firstChunkReceived = true;
            console.log('✅ First video chunk received - MediaRecorder is working correctly');
            console.log('  Chunk size:', event.data.size, 'bytes');
            console.log('  Chunk type:', event.data.type || 'unspecified');
            console.log('  Drawing frames so far:', window.mouthFrameCount || 0);
            console.log('  Time to first chunk:', (Date.now() - recordingStartTime), 'ms');
          }

          // Log every 5th chunk to monitor progress more frequently
          if (totalChunksReceived % 5 === 0) {
            const avgChunkSize = Math.round(totalDataSize / totalChunksReceived);
            const recordingTime = (Date.now() - recordingStartTime) / 1000;
            const dataRate = (totalDataSize / 1024) / recordingTime; // KB/s

            console.log('👄 Video chunk progress:', {
              chunkNumber: totalChunksReceived,
              size: event.data.size,
              type: event.data.type || 'unspecified',
              totalSize: (totalDataSize / 1024).toFixed(1) + 'KB',
              avgChunkSize: avgChunkSize + 'B',
              dataRate: dataRate.toFixed(1) + 'KB/s',
              drawingFrames: window.mouthFrameCount || 0,
              recordingTime: recordingTime.toFixed(1) + 's'
            });
          }

          // Validate chunk size is reasonable for video data
          if (event.data.size < 50) {
            console.warn('⚠️ Very small chunk received - may indicate recording issues');
          }

        } else {
          console.error('❌ CRITICAL: Empty or null chunk received from MediaRecorder');
          console.error('  Event data:', event.data);
          console.error('  Data size:', event.data ? event.data.size : 'null/undefined');
          console.error('  MediaRecorder state:', mediaRecorder.state);
          console.error('  Stream active:', mouthStream.active);
          console.error('  Drawing frames:', window.mouthFrameCount || 0);
          console.error('  This indicates MediaRecorder or canvas stream failure');
        }
      };

      // Clean up monitor when recording stops
      const originalOnStop = mediaRecorder.onstop;
      mediaRecorder.onstop = (...args) => {
        clearInterval(chunkMonitor);
        if (originalOnStop) originalOnStop.apply(mediaRecorder, args);
      };

      // Add error handling for privacy-compliant MediaRecorder
      mediaRecorder.onerror = (event) => {
        console.error('👄 Privacy-compliant MediaRecorder error:', event.error);

        // CRITICAL CLEANUP: Reset recording ref on error
        isRecordingRef.current = false;
        setIsRecording(false);

        setErrorMessage('Recording failed: Mouth recording error. Please try again.');
        setProcessing(false);
        setProcessingPhase('');
        setUploadProgress(0);

        // Stop mouth region drawing on error
        if (mouthRecordingRef.current) {
          cancelAnimationFrame(mouthRecordingRef.current);
          mouthRecordingRef.current = null;
        }
      };

      // Handle recording completion
      let recordingCompleted = false;
      let videoBlob = null;

      const handleRecordingCompletion = async () => {
        if (recordingCompleted) {
          console.log('🎬 Recording completed, processing...');
          console.log('  🚀 Calling handleRecordingComplete with video...');
          await handleRecordingComplete(videoBlob, null);
          console.log('  ✅ handleRecordingComplete completed');
        }
      };

      mediaRecorder.onstop = async () => {
        console.log('👄 Privacy-compliant MediaRecorder onstop event triggered');
        console.log('  recordedChunks length:', recordedChunks.length);
        console.log('  recordedChunks details:', recordedChunks.map(chunk => ({ size: chunk.size, type: chunk.type })));

        // CRITICAL VALIDATION: Ensure we have valid video chunks
        if (recordedChunks.length === 0) {
          console.error('❌ No video chunks recorded - MediaRecorder failed to capture data');
          setErrorMessage('Recording failed: No video data captured. Please try again.');
          setProcessing(false);
          setProcessingPhase('');
          setUploadProgress(0);
          setIsRecording(false);
          return;
        }

        // Validate chunk types
        const invalidChunks = recordedChunks.filter(chunk => !chunk.type.includes('video') && !chunk.type.includes('webm'));
        if (invalidChunks.length > 0) {
          console.warn('⚠️ Some chunks may not be valid video data:', invalidChunks);
        }

        // CRITICAL VALIDATION: Ensure we have valid video chunks before creating blob
        const totalChunkSize = recordedChunks.reduce((sum, chunk) => sum + chunk.size, 0);
        const averageChunkSize = totalChunkSize / recordedChunks.length;

        // COMPREHENSIVE VIDEO ANALYSIS: Validate frame count and data quality
        const recordingDuration = 5; // seconds
        const expectedMinChunks = recordingDuration * 10; // 10 chunks/second with 100ms timeslice
        const expectedMinFrames = recordingDuration * 20; // Minimum 20fps for 5 seconds = 100 frames
        const drawingFrames = window.mouthFrameCount || 0;

        console.log('📊 Comprehensive video analysis:', {
          totalChunks: recordedChunks.length,
          expectedMinChunks: expectedMinChunks,
          totalSize: (totalChunkSize / 1024).toFixed(1) + 'KB',
          averageChunkSize: averageChunkSize.toFixed(0) + 'B',
          chunkTypes: [...new Set(recordedChunks.map(chunk => chunk.type || 'unspecified'))],
          drawingFrames: drawingFrames,
          expectedMinFrames: expectedMinFrames,
          frameRateAchieved: (drawingFrames / recordingDuration).toFixed(1) + 'fps',
          dataRate: (totalChunkSize / 1024 / recordingDuration).toFixed(1) + 'KB/s'
        });

        // Enhanced validation for video quality
        if (totalChunkSize < 10000) { // Increased minimum to 10KB for 5-second video
          console.error('❌ CRITICAL: Video data too small - likely recording failure');
          console.error('  Total size:', (totalChunkSize / 1024).toFixed(1), 'KB');
          console.error('  Expected minimum: 10KB for 5-second video');
          setErrorMessage('Recording failed: Insufficient video data captured. Please try again.');
          setProcessing(false);
          setProcessingPhase('');
          setUploadProgress(0);
          setIsRecording(false);
          return;
        }

        // Validate chunk count
        if (recordedChunks.length < expectedMinChunks / 2) {
          console.warn('⚠️ Low chunk count - may indicate frame dropping');
          console.warn('  Chunks received:', recordedChunks.length);
          console.warn('  Expected minimum:', expectedMinChunks);
        }

        // Validate drawing frame count
        if (drawingFrames < expectedMinFrames) {
          console.warn('⚠️ Low drawing frame count - may affect video quality');
          console.warn('  Drawing frames:', drawingFrames);
          console.warn('  Expected minimum:', expectedMinFrames);
          console.warn('  Achieved frame rate:', (drawingFrames / recordingDuration).toFixed(1), 'fps');
        }

        // Create video blob with explicit video MIME type
        videoBlob = new Blob(recordedChunks, { type: 'video/webm' });

        // FINAL VALIDATION: Ensure blob is actually a video file
        if (videoBlob.type !== 'video/webm') {
          console.warn('⚠️ Video blob type mismatch:', videoBlob.type);
        }

        console.log('  ✅ Privacy-compliant videoBlob created successfully:', {
          size: videoBlob.size,
          type: videoBlob.type,
          contentType: 'mouth-region-only',
          privacyCompliant: true,
          chunksUsed: recordedChunks.length,
          isValidVideoSize: videoBlob.size > 5000,
          dimensions: '150x75 pixels',
          aspectRatio: '2:1'
        });

        // Stop mouth region drawing
        if (mouthRecordingRef.current) {
          cancelAnimationFrame(mouthRecordingRef.current);
          mouthRecordingRef.current = null;
          console.log('👄 Mouth region drawing stopped after recording completion');
        }

        // Validate video blob before proceeding
        if (videoBlob.size === 0) {
          console.error('❌ Privacy-compliant video blob is empty! This will cause validation failure.');
          console.error('  Possible causes:');
          console.error('  - MediaRecorder failed to capture mouth canvas data');
          console.error('  - Mouth canvas stream was not properly initialized');
          console.error('  - Recording was stopped too quickly');
          console.error('  - Mouth region drawing was not active');
          console.error('  - Canvas content was not being drawn properly');

          // CRITICAL FIX: Add more detailed debugging information
          console.error('  🔍 DEBUGGING INFO:');
          console.error('    recordedChunks length:', recordedChunks.length);
          console.error('    recordedChunks sizes:', recordedChunks.map(chunk => chunk.size));
          console.error('    mouth canvas size:', mouthCanvasRef.current ? `${mouthCanvasRef.current.width}x${mouthCanvasRef.current.height}` : 'null');
          console.error('    mouth stream active:', mouthStream ? mouthStream.active : 'null');
          console.error('    mouth stream tracks:', mouthStream ? mouthStream.getVideoTracks().length : 'null');

          // CRITICAL FIX: Do NOT use canvas.toBlob() fallback as it creates static images
          // This was causing the privacy violation by saving still images instead of videos
          console.error('❌ CRITICAL ERROR: MediaRecorder failed to capture video data');
          console.error('🚫 Canvas fallback disabled to prevent static image creation');
          console.error('💡 This prevents privacy violations from saving still images with visible eyes');

          // Instead of fallback, provide clear error message and stop processing
          setErrorMessage('Recording failed: Video capture error. Please try recording again with better lighting and ensure your camera is working properly.');
          setProcessing(false);
          setProcessingPhase('');
          setUploadProgress(0);
          setIsRecording(false);

          // Stop mouth region drawing
          if (mouthRecordingRef.current) {
            cancelAnimationFrame(mouthRecordingRef.current);
            mouthRecordingRef.current = null;
          }

          return;
        }

        recordingCompleted = true;
        await handleRecordingCompletion();
      };

      setIsRecording(true);
      setRecordingTimer(5);

      // CRITICAL FIX: Start MediaRecorder with proper validation and error handling
      console.log('👄 Starting privacy-compliant mouth recording...');
      console.log('🔒 Recording mouth region only - no eyes, no audio, no upper face');

      try {
        // CRITICAL: Verify canvas is actively updating before starting MediaRecorder
        const initialFrameCount = window.mouthFrameCount || 0;
        await new Promise(resolve => setTimeout(resolve, 500)); // Wait for drawing activity
        const currentFrameCount = window.mouthFrameCount || 0;

        if (currentFrameCount <= initialFrameCount) {
          console.error('❌ CRITICAL: Canvas drawing loop is not active');
          console.error('  Initial frames:', initialFrameCount);
          console.error('  Current frames:', currentFrameCount);
          throw new Error('Canvas drawing loop is not updating - MediaRecorder will receive empty data');
        }

        console.log('✅ Canvas drawing loop confirmed active:', {
          framesDrawn: currentFrameCount,
          drawingRate: (currentFrameCount - initialFrameCount) / 0.5 + ' fps'
        });

        // OPTIMIZED TIMESLICE: Start recording with enhanced data capture frequency
        mediaRecorder.start(100); // Reduced to 100ms for better frame capture (was 200ms)

        // Verify recording actually started and monitor initial data flow
        setTimeout(() => {
          if (mediaRecorder.state !== 'recording') {
            console.error('❌ CRITICAL: MediaRecorder failed to start recording');
            console.error('  Current state:', mediaRecorder.state);
            console.error('  Expected state: recording');
            throw new Error(`MediaRecorder failed to start - state: ${mediaRecorder.state}`);
          } else {
            console.log('✅ MediaRecorder confirmed in recording state');
            console.log('  Drawing frames since start:', window.mouthFrameCount - currentFrameCount);
          }
        }, 300);

        // Monitor for first chunk within reasonable time
        setTimeout(() => {
          if (totalChunksReceived === 0) {
            console.error('❌ CRITICAL: No chunks received after 1 second of recording');
            console.error('  MediaRecorder state:', mediaRecorder.state);
            console.error('  Stream active:', mouthStream.active);
            console.error('  Drawing frames:', window.mouthFrameCount || 0);
            console.error('  This indicates a fundamental MediaRecorder/canvas integration issue');
          }
        }, 1000);

        console.log('👄 Privacy-compliant mouth recording started successfully');
        console.log('📊 Recording parameters:', {
          state: mediaRecorder.state,
          mimeType: mediaRecorder.mimeType || 'default',
          streamActive: mouthStream.active,
          canvasSize: `${mouthCanvas.width}x${mouthCanvas.height}`,
          drawingFrames: window.mouthFrameCount || 0
        });

      } catch (startError) {
        console.error('❌ CRITICAL: Failed to start MediaRecorder:', startError);

        // CRITICAL CLEANUP: Reset recording ref on failure
        isRecordingRef.current = false;
        setIsRecording(false);

        // Stop any active drawing loops
        if (mouthRecordingRef.current) {
          cancelAnimationFrame(mouthRecordingRef.current);
          mouthRecordingRef.current = null;
        }

        setErrorMessage('Recording failed to start. Please refresh the page and try again.');
        setProcessing(false);
        setProcessingPhase('');
        setUploadProgress(0);
        return;
      }

      // ENHANCED 5-SECOND COUNTDOWN: Precise timing with comprehensive logging
      let countdown = 5;
      console.log('⏱️ Starting 5-second countdown timer');

      const timerInterval = setInterval(() => {
        countdown--;
        setRecordingTimer(countdown);
        console.log('⏱️ Countdown:', countdown, '(', 5 - countdown, 'seconds elapsed)');

        if (countdown <= 0) {
          clearInterval(timerInterval);
          console.log('⏱️ Countdown complete - 5 seconds recorded');

          // Stop privacy-compliant mouth recording
          if (mediaRecorder.state === 'recording') {
            mediaRecorder.stop();
            console.log('👄 Privacy-compliant mouth recording stopped via countdown');
            console.log('🔒 Mouth-region-only recording completed');

            // Final frame count logging
            const finalFrameCount = window.mouthFrameCount || 0;
            const finalFPS = finalFrameCount / 5;
            console.log('📊 Final recording statistics:');
            console.log('  Total frames drawn:', finalFrameCount);
            console.log('  Average FPS:', finalFPS.toFixed(1));
            console.log('  Target FPS: 30');
            console.log('  Frame rate achievement:', ((finalFPS / 30) * 100).toFixed(1) + '%');
          }

          setRecordingTimer(0);
        }
      }, 1000); // Precise 1-second intervals

      // Stop recording after 5 seconds (backup)
      setTimeout(() => {
        clearInterval(timerInterval);

        // Stop privacy-compliant mouth recording (backup)
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
          console.log('👄 Privacy-compliant mouth recording stopped (backup)');
        }

        setRecordingTimer(0);
      }, 5000);

    } catch (error) {
      console.error('=== ERROR DURING RECORDING START ===');
      console.error('Error details:', error);
      console.error('Error stack:', error.stack);

      // Provide more specific error messages for recording start failures
      let errorMessage = 'Failed to start recording';
      if (error.message) {
        if (error.message.includes('camera') || error.message.includes('video')) {
          errorMessage = 'Camera access error. Please check camera permissions and try again.';
        } else if (error.message.includes('MediaRecorder')) {
          errorMessage = 'Recording not supported by your browser. Please try a different browser.';
        } else if (error.message.includes('stream')) {
          errorMessage = 'Video stream error. Please refresh the page and try again.';
        } else {
          errorMessage = `Recording start failed: ${error.message}`;
        }
      }

      setErrorMessage(errorMessage);
      setIsRecording(false);
      setRecordingTimer(0);
    }
  }, [handleRecordingComplete]);

  // Initialize camera and face detection model when component mounts
  useEffect(() => {
    const initModel = async () => {
      try {
        console.log('🎯 Initializing TensorFlow.js...');
        await tf.ready();
        console.log('🎯 TensorFlow.js ready');

        console.log('🎯 Loading MediaPipe FaceMesh model...');

        // Try MediaPipe runtime first
        try {
          const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh;
          const detectorConfig = {
            runtime: 'mediapipe',
            solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh',
            refineLandmarks: false,
          };

          const loadedModel = await faceLandmarksDetection.createDetector(model, detectorConfig);
          console.log('🎯 MediaPipe FaceMesh model loaded successfully');
          setModel(loadedModel);
          return;
        } catch (mediapipeError) {
          console.warn('🎯 MediaPipe runtime failed, trying TFJS runtime:', mediapipeError);

          // Fallback to TFJS runtime
          try {
            const model = faceLandmarksDetection.SupportedModels.MediaPipeFaceMesh;
            const detectorConfig = {
              runtime: 'tfjs',
              refineLandmarks: false,
            };

            const loadedModel = await faceLandmarksDetection.createDetector(model, detectorConfig);
            console.log('🎯 TFJS FaceMesh model loaded successfully as fallback');
            setModel(loadedModel);
            return;
          } catch (tfjsError) {
            console.warn('🎯 TFJS runtime also failed:', tfjsError);
            throw new Error('Both MediaPipe and TFJS runtimes failed');
          }
        }
      } catch (error) {
        console.error('🎯 Error initializing face detection model:', error);
        console.warn('🔄 Face detection unavailable - using center crop fallback for mouth region');
        console.warn('  This does not affect privacy-compliant recording functionality');

        // Don't show error message to user - face detection is optional
        // The mouth region drawing uses robust center crop fallback
        setModel(null);

        // Clear any error message since this is not critical
        setErrorMessage('');
      }
    };

    checkCameraPermissions();
    initModel();

    return () => {
      // CRITICAL CLEANUP: Ensure recording ref is stopped
      isRecordingRef.current = false;

      if (detectionRef.current) {
        cancelAnimationFrame(detectionRef.current);
      }
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
      }
      if (mouthRecordingRef.current) {
        cancelAnimationFrame(mouthRecordingRef.current);
      }
      // Stop LipNet processing to prevent infinite loops
      setLipnetProcessingActive(false);
      cleanupLipNetCanvas();
      cleanupMouthCanvas();
    };
  }, [checkCameraPermissions]);

  // Start face detection when model is loaded
  useEffect(() => {
    if (permissionGranted) {
      // Start detection loop regardless of model availability
      detectFace();

      // DISABLED: LipNet processing for debugging to prevent infinite loops
      // The mouth canvas optimization provides equivalent functionality
      console.log('🎯 LipNet processing disabled to prevent infinite loops');
      console.log('📊 Using mouth canvas optimization instead for better performance');

      // Don't start LipNet processing to avoid infinite loops
      // initializeLipNetCanvas();
      // setLipnetProcessingActive(true);
    }

    return () => {
      if (detectionRef.current) {
        cancelAnimationFrame(detectionRef.current);
      }
      if (frameProcessingRef.current) {
        cancelAnimationFrame(frameProcessingRef.current);
      }
      // Ensure LipNet processing is stopped
      setLipnetProcessingActive(false);
    };
  }, [model, permissionGranted, detectFace]);

  // Test AWS connection on component mount
  useEffect(() => {
    const testConnection = async () => {
      console.log('🧪 Testing AWS connection on VideoRecorder mount...');

      try {
        const result = await testAWSConnection();
        if (result.success) {
          console.log('✅ AWS connection test passed:', result);
          console.log('🔧 AWS Configuration Status:');
          console.log('  - Identity Pool ID configured:', !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID);
          console.log('  - Region configured:', process.env.REACT_APP_AWS_REGION);
          console.log('  - Bucket configured:', process.env.REACT_APP_S3_BUCKET);
        } else {
          console.warn('⚠️ AWS connection test failed:', result);
          console.warn('🔧 This may indicate AWS credentials or permissions issues');
        }
      } catch (error) {
        console.error('❌ AWS connection test error:', error);
        console.error('🔧 Check AWS configuration in .env file');
      }
    };

    testConnection();
  }, []);

  // Removed complex synchronization logic - using direct parent data

  // Removed all complex debugging and synchronization logic

  // Track phrase changes for debugging
  useEffect(() => {
    console.log('📝 VideoRecorder: Phrase prop changed to:', phrase);
    console.log('📝 VideoRecorder: Category prop changed to:', category);
    console.log('📝 VideoRecorder: BLACK OVERLAY TEXT should now display:', phrase);
    console.log('📝 VideoRecorder: DOM should update immediately with new phrase text');

    // Force a re-render to ensure phrase text updates
    if (phrase) {
      console.log('📝 VideoRecorder: Phrase text confirmed for overlay:', phrase);
    }
  }, [phrase, category]);



  // Webcam component with optimized mouth-region recording
  // PERFORMANCE OPTIMIZATION: Records only mouth region directly via canvas,
  // eliminating 800-1500ms post-processing oval cropping delay
  return (
    <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
      {/* Layout: oval centered, instructions positioned to the left */}
      <Box
        sx={{
          position: 'relative',
          width: '100%',
          height: '60vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mt: 0.5,
        }}
      >
        {/* Instruction Box - positioned to the left of oval */}
        <Box
          sx={{
            width: { xs: '100%', md: '280px' },
            maxWidth: { xs: '100%', md: '280px' },
            bgcolor: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 2,
            p: 3,
            border: '2px solid #2196F3',
            boxShadow: '0 4px 16px rgba(33, 150, 243, 0.2)',
            mb: { xs: 1, md: 0 },
            position: { xs: 'relative', md: 'absolute' },
            left: { xs: 'auto', md: 'calc(50% - 190px - 280px - 40px)' },
            top: { xs: 'auto', md: '50%' },
            transform: { xs: 'none', md: 'translateY(-50%)' },
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: '#1976d2',
              fontWeight: 'bold',
              mb: 2,
              textAlign: 'center',
              fontSize: { xs: '1.1rem', sm: '1.25rem' }
            }}
          >
            Recording Instructions
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: '#1976d2',
                  fontWeight: 'bold',
                  minWidth: '20px',
                  fontSize: '1rem'
                }}
              >
                1.
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#333',
                  lineHeight: 1.4,
                  fontSize: '0.95rem'
                }}
              >
                Position your lips over the lip guide shown using the zoom if needed. Ensure your eyes are <strong>not</strong> visible
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: '#1976d2',
                  fontWeight: 'bold',
                  minWidth: '20px',
                  fontSize: '1rem'
                }}
              >
                2.
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#333',
                  lineHeight: 1.4,
                  fontSize: '0.95rem'
                }}
              >
                Press <strong>Record</strong> and silently mouth what is shown in the oval <strong>one time</strong>
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: '#1976d2',
                  fontWeight: 'bold',
                  minWidth: '20px',
                  fontSize: '1rem'
                }}
              >
                3.
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#333',
                  lineHeight: 1.4,
                  fontSize: '0.95rem'
                }}
              >
                Press <strong>Stop</strong> when you've finished mouthing it
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
              <Typography
                variant="body2"
                sx={{
                  color: '#1976d2',
                  fontWeight: 'bold',
                  minWidth: '20px',
                  fontSize: '1rem'
                }}
              >
                4.
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#333',
                  lineHeight: 1.4,
                  fontSize: '0.95rem'
                }}
              >
                The next word will load automatically – <strong>repeat</strong> until you're done
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Progress Tally Box - positioned to the right of oval at same height as instruction box */}
        <Box
          sx={{
            width: { xs: '100%', md: '280px' },
            maxWidth: { xs: '100%', md: '280px' },
            bgcolor: 'rgba(255, 255, 255, 0.95)',
            borderRadius: 2,
            p: 3,
            border: '2px solid #2196F3',
            boxShadow: '0 4px 16px rgba(33, 150, 243, 0.2)',
            mb: { xs: 1, md: 0 },
            position: { xs: 'relative', md: 'absolute' },
            right: { xs: 'auto', md: 'calc(50% - 190px - 280px - 40px)' },
            top: { xs: 'auto', md: '50%' },
            transform: { xs: 'none', md: 'translateY(-50%)' },
            zIndex: 1,
          }}
        >
          {/* Current Phrase Progress */}
          <Typography
            variant="h6"
            sx={{
              color: '#1976d2',
              fontWeight: 'bold',
              mb: 2,
              textAlign: 'center',
              fontSize: { xs: '1.1rem', sm: '1.25rem' }
            }}
          >
            Current Phrase Progress
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3 }}>
            {[1, 2, 3].map((num) => (
              <Box
                key={num}
                sx={{
                  width: 16,
                  height: 16,
                  borderRadius: '50%',
                  bgcolor: currentPhraseRecordingCount >= num ? '#4caf50' : 'transparent',
                  border: currentPhraseRecordingCount >= num ? '2px solid #4caf50' : '2px solid #bdbdbd',
                  transition: 'all 0.3s ease'
                }}
              />
            ))}
          </Box>

          {/* Session Progress */}
          <Typography
            variant="h6"
            sx={{
              color: '#1976d2',
              fontWeight: 'bold',
              mb: 1,
              textAlign: 'center',
              fontSize: { xs: '1.1rem', sm: '1.25rem' }
            }}
          >
            Session Progress
          </Typography>

          <Typography
            variant="body1"
            sx={{
              textAlign: 'center',
              color: '#333',
              fontSize: '0.95rem'
            }}
          >
            {`${progressData.completedPhrases} of ${progressData.totalPhrases} phrases completed`}
          </Typography>
        </Box>

        {/* Oval/Camera Box - CENTERED on page */}
        <Box
          sx={{
            position: 'relative',
            width: { xs: '300px', sm: '340px', md: '380px' },
            height: { xs: '420px', sm: '480px', md: '540px' },
            borderRadius: '50%',
            overflow: 'hidden',
            bgcolor: '#000',
            border: '4px solid #2196F3',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 8px 32px rgba(33, 150, 243, 0.3)',
            transform: 'scaleY(1.0)',
          }}
        >
          {permissionGranted && !cameraError ? (
            <>
              <Webcam
                ref={webcamRef}
                audio={false}
                videoConstraints={getVideoConstraints()}
                mirrored={false}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: 'auto',
                  height: '100%',
                  minWidth: '100%',
                  objectFit: 'cover',
                  transform: `translate(-50%, -50%) scale(${zoomLevel}) scaleX(-1)`,
                  transformOrigin: 'center center'
                }}
                onLoadedMetadata={() => {
                  if (webcamRef.current && webcamRef.current.video) {
                    const video = webcamRef.current.video;
                    if (video.videoWidth > 0 && video.videoHeight > 0) {
                      updateCameraResolution(video);
                    } else {
                      console.warn('Video metadata loaded but dimensions not yet available');
                    }
                  }
                }}
              />
              <canvas
                ref={canvasRef}
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: '120%',
                  height: '120%',
                  transform: 'translate(-50%, -50%) scaleX(-1)',
                  pointerEvents: 'none'
                }}
                width={VIDEO_CONSTRAINTS.width}
                height={VIDEO_CONSTRAINTS.height}
              />
              {/* Semi-transparent lip guide overlay for optimal mouth positioning */}
              {permissionGranted && !isRecording && (
                <img
                  src="/images/lips-no-bg.png"
                  alt="Lip positioning guide"
                  style={{
                    position: 'absolute',
                    top: '75%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) scaleX(-1)',
                    width: '41%',
                    height: 'auto',
                    opacity: 0.25,
                    pointerEvents: 'none',
                    zIndex: 3,
                    transition: 'opacity 0.3s ease-in-out',
                    objectFit: 'contain',
                    maxWidth: '150px',
                    filter: 'brightness(1.2) contrast(1.1)'
                  }}
                />
              )}
              {/* Black overlay with phrase text inside oval viewport */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '50%',
                  background: 'rgba(0,0,0,1.0)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 5,
                  pointerEvents: 'none'
                }}
              >
                <Typography
                  key={phrase}
                  variant="h5"
                  sx={{
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
                    fontSize: { xs: '1.2rem', sm: '1.4rem', md: '1.6rem' },
                    px: 2,
                    lineHeight: 1.2,
                    transition: 'opacity 0.3s ease-in-out',
                    opacity: phrase ? 1 : 0.7
                  }}
                >
                  {phrase || 'Loading phrase...'}
                </Typography>
              </Box>
            </>
          ) : (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: '#333',
                color: 'white'
              }}
            >
              <Typography>Camera Loading...</Typography>
            </Box>
          )}
          {/* Controls container - positioned below oval with proper spacing - only show when camera is active */}
          {permissionGranted && !cameraError && (
            <Box
              sx={{
                position: 'absolute',
                bottom: { xs: -180, sm: -170, md: -160 },
                left: '50%',
                transform: 'translateX(-50%)',
                width: '100%',
                maxWidth: { xs: 350, sm: 400, md: 450 },
                bgcolor: 'rgba(0, 0, 0, 0.9)',
                borderRadius: 2,
                p: 3,
                zIndex: 10,
                border: '1px solid rgba(255, 255, 255, 0.2)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)'
              }}
            >
              {/* Camera selection */}
              {availableCameras.length > 1 && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ color: 'white', mb: 1 }}>
                    Select Camera:
                  </Typography>
                  <select
                    value={selectedCamera}
                    onChange={(e) => setSelectedCamera(e.target.value)}
                    style={{
                      width: '100%',
                      padding: '8px',
                      borderRadius: '4px',
                      border: 'none',
                      fontSize: '14px'
                    }}
                  >
                    {availableCameras.map((camera) => (
                      <option key={camera.deviceId} value={camera.deviceId}>
                        {camera.label || `Camera ${camera.deviceId.slice(0, 8)}`}
                      </option>
                    ))}
                  </select>
                </Box>
              )}
              {/* Recording status and mouth tracking */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FiberManualRecordIcon
                    sx={{
                      color: isRecording ? 'error.main' : 'success.main',
                      fontSize: 12
                    }}
                  />
                  <Typography variant="body2" sx={{ color: 'white' }}>
                    {isRecording ? 'Recording...' : 'Ready'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <CircleIcon
                    sx={{
                      color: model ? (mouthTrackingQuality > 0.7 ? 'success.main' : 'warning.main') : 'info.main',
                      fontSize: 12
                    }}
                  />
                  <Typography variant="body2" sx={{ color: 'white' }}>
                    {!model ? 'Manual Mode' : (mouthTrackingQuality > 0.7 ? 'Good' : 'Adjust Position')}
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
        </Box>
      </Box>

      {/* Controls row: Zoom slider below oval, completed dots to the right */}
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 0, mb: 0 }}>
        {/* Zoom control directly under oval */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            maxWidth: 400
          }}
        >
          <Typography variant="body2" sx={{ color: 'text.primary', mb: 1, fontWeight: 'bold' }}>
            Zoom: {zoomLevel.toFixed(1)}x
          </Typography>
          <Box sx={{ position: 'relative', width: '95%', display: 'flex', alignItems: 'center' }}>
            <Slider
              value={zoomLevel}
              min={1}
              max={3}
              step={0.1}
              onChange={(_, value) => setZoomLevel(value)}
              sx={{
                color: '#1976d2',
                height: 8,
                width: '100%',
                '& .MuiSlider-thumb': {
                  bgcolor: '#1976d2',
                  width: 20,
                  height: 20,
                  '&:hover': {
                    boxShadow: '0 0 0 8px rgba(25, 118, 210, 0.16)'
                  }
                },
                '& .MuiSlider-track': {
                  bgcolor: '#1976d2',
                  height: 8
                },
                '& .MuiSlider-rail': {
                  bgcolor: '#bdbdbd',
                  height: 8
                }
              }}
            />
            <Typography
              variant="caption"
              sx={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                color: '#333333',
                fontWeight: 'bold',
                fontSize: '0.7rem',
                textShadow: '1px 1px 2px rgba(0,0,0,0.8)',
                pointerEvents: 'none',
                zIndex: 10,
                letterSpacing: '0.5px'
              }}
            >
              ZOOM
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* 5-Second Timer positioned underneath the tally box */}
      {isRecording && (
        <Box
          sx={{
            position: { xs: 'relative', md: 'absolute' },
            right: { xs: 'auto', md: 'calc(50% - 190px - 280px - 40px + 100px)' }, // Centered under tally box
            top: { xs: 'auto', md: 'calc(50% + 150px)' }, // Positioned below tally box
            transform: { xs: 'none', md: 'translateX(-50%)' },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: 80,
            height: 80,
            bgcolor: 'rgba(0, 0, 0, 0.8)',
            borderRadius: '50%',
            border: '3px solid #f44336',
            zIndex: 15,
            mt: { xs: 2, md: 0 }, // Add margin top for mobile
            mx: { xs: 'auto', md: 0 } // Center on mobile
          }}
        >
          <Typography
            variant="h3"
            sx={{
              color: 'white',
              fontWeight: 'bold',
              fontSize: { xs: '2rem', sm: '2.5rem' },
              lineHeight: 1
            }}
          >
            {recordingTimer}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              color: 'white',
              fontSize: '0.7rem',
              mt: 0.5
            }}
          >
            SEC
          </Typography>
        </Box>
      )}

          {cameraError && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(244, 67, 54, 0.1)', borderRadius: 2 }}>
              <Typography variant="body2" color="error" gutterBottom>
                <strong>Camera Error:</strong> {errorMessage}
              </Typography>
              {isMobile && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'rgba(0,150,136,0.1)', borderRadius: 2 }}>
                  <Typography variant="body2" align="left">
                    <strong>Mobile Tips:</strong>
                    <ul style={{ paddingLeft: '20px', textAlign: 'left' }}>
                      <li>Make sure to allow camera permissions when prompted</li>
                      <li>On iOS, Safari is recommended as it may allow camera access on local networks</li>
                      <li>For iOS: Go to Settings &gt; Safari &gt; Camera &gt; Allow for this website</li>
                      <li>For Android: Try using Chrome and ensure all permissions are granted</li>
                      <li>Refresh the page after granting permissions</li>
                    </ul>
                  </Typography>
                </Box>
              )}
              <Button
                variant="contained"
                color="primary"
                onClick={() => window.location.reload()}
                sx={{ mt: 3 }}
              >
                Refresh Page
              </Button>
            </Box>
          )}

          {!permissionGranted && !cameraError && (
            <WebcamPermissionHandler
              onPermissionGranted={() => setPermissionGranted(true)}
              onError={(msg) => {
                setErrorMessage(msg);
                setCameraError(true);
              }}
            />
          )}

          {/* Main controls container with recording buttons centered */}
          <Box sx={{ mt: 0, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 40 }}>
        {/* Primary recording controls - centered */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          {!isRecording ? (
            <>
              <Button
                variant="contained"
                color="primary"
                onClick={handleStartRecording}
                disabled={disabled || cameraError || processing}
                sx={{
                  minWidth: 120,
                  opacity: processing ? 0.6 : 1
                }}
              >
                {processing
                  ? (processingPhase === 'uploading' ? 'Uploading...' : 'Processing...')
                  : 'Start Recording'
                }
              </Button>

              {canReRecord && (
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    console.log('Record Again clicked - starting new recording');
                    // Don't reset recording count - let parent manage it
                    handleStartRecording();
                  }}
                  disabled={disabled || cameraError || processing}
                  sx={{
                    minWidth: 120,
                    opacity: processing ? 0.6 : 1
                  }}
                >
                  {processing
                    ? (processingPhase === 'uploading' ? 'Uploading...' : 'Processing...')
                    : 'Record Again'
                  }
                </Button>
              )}
            </>
          ) : (
            <Button
              variant="contained"
              color="error"
              onClick={handleStopRecording}
              disabled={processing}
              sx={{
                minWidth: 120,
                opacity: processing ? 0.6 : 1
              }}
            >
              {processing ? 'Processing...' : 'Stop Recording'}
            </Button>
          )}
        </Box>

        {/* Early exit button removed */}
      </Box>

      {processing && (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5, mt: 0.5 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <CircularProgress size={24} />
            <Typography>
              {processingPhase === 'uploading'
                ? 'Uploading to AWS S3...'
                : 'Processing video...'
              }
            </Typography>
          </Box>
          {processingPhase === 'uploading' && uploadProgress > 0 && (
            <Box sx={{ width: '100%', maxWidth: 200 }}>
              <Typography variant="caption" sx={{ color: 'text.secondary', mb: 0.5 }}>
                Upload Progress: {uploadProgress}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={uploadProgress}
                sx={{ height: 4, borderRadius: 2 }}
              />
            </Box>
          )}
          <Typography variant="caption" sx={{ color: 'text.secondary', textAlign: 'center' }}>
            Please wait - recording buttons are disabled during upload
          </Typography>
        </Box>
      )}
      
      {errorMessage && (
        <Alert severity="error" sx={{ mt: 1 }}>
          {errorMessage}
        </Alert>
      )}

      {/* Upload mode indicator */}
      {isBackendUploadMode && !isDevelopmentMode && (
        <Alert severity="success" sx={{ mt: 1 }}>
          <strong>Backend Upload Mode:</strong> Videos are being uploaded to AWS S3 via secure backend server.
        </Alert>
      )}

      {/* Development mode indicator */}
      {isDevelopmentMode && (
        <Alert severity="warning" sx={{ mt: 1 }}>
          <strong>Development Mode:</strong> Backend server not configured. Recordings will be simulated locally.
        </Alert>
      )}



      {/* Snackbar for notifications */}
      <Snackbar
        open={showSavedNotification}
        autoHideDuration={3000}
        onClose={() => setShowSavedNotification(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert severity="success" variant="filled">
          Recordings saved successfully!
        </Alert>
      </Snackbar>

      {/* Early exit dialog removed */}
    </Box>
  );
};

export default VideoRecorder;
